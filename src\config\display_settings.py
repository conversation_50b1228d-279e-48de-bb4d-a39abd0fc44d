"""
显示设置配置
定义固定分辨率和界面布局参数
"""

# 预定义的固定分辨率选项
RESOLUTION_PRESETS = {
    "standard": {
        "name": "标准分辨率",
        "width": 1280,
        "height": 800,
        "description": "适合大多数显示器的标准分辨率"
    },
    "large": {
        "name": "大屏分辨率", 
        "width": 1440,
        "height": 900,
        "description": "适合大屏显示器的高分辨率"
    },
    "compact": {
        "name": "紧凑分辨率",
        "width": 1024,
        "height": 640,
        "description": "适合小屏幕或笔记本电脑"
    },
    "wide": {
        "name": "宽屏分辨率",
        "width": 1600,
        "height": 900,
        "description": "适合宽屏显示器"
    }
}

# 默认使用的分辨率
DEFAULT_RESOLUTION = "standard"

# 最小分辨率要求
MIN_WIDTH = 1024
MIN_HEIGHT = 640

# 界面布局参数
LAYOUT_SETTINGS = {
    "header_height": 120,
    "sidebar_width": 300,
    "panel_spacing": 10,
    "button_height": 32,
    "input_height": 28,
    "group_margin": 15,
    "content_margin": 20
}

# 字体设置
FONT_SETTINGS = {
    "default_family": "Microsoft YaHei UI",
    "default_size": 9,
    "header_size": 14,
    "button_size": 9,
    "status_size": 8
}

# 颜色主题
COLOR_THEME = {
    "primary": "#4A90E2",
    "success": "#28a745", 
    "warning": "#ffc107",
    "danger": "#dc3545",
    "background": "#f8f9fa",
    "border": "#dee2e6",
    "text": "#495057",
    "text_light": "#6c757d"
}

def get_resolution_config(preset_name=None):
    """
    获取分辨率配置
    
    Args:
        preset_name: 预设名称，如果为None则使用默认
        
    Returns:
        dict: 分辨率配置
    """
    if preset_name is None:
        preset_name = DEFAULT_RESOLUTION
    
    if preset_name not in RESOLUTION_PRESETS:
        preset_name = DEFAULT_RESOLUTION
    
    return RESOLUTION_PRESETS[preset_name]

def validate_resolution(width, height):
    """
    验证分辨率是否符合最小要求
    
    Args:
        width: 宽度
        height: 高度
        
    Returns:
        tuple: (is_valid, adjusted_width, adjusted_height)
    """
    adjusted_width = max(width, MIN_WIDTH)
    adjusted_height = max(height, MIN_HEIGHT)
    
    is_valid = (width >= MIN_WIDTH and height >= MIN_HEIGHT)
    
    return is_valid, adjusted_width, adjusted_height

def get_optimal_resolution_for_screen(screen_width, screen_height):
    """
    根据屏幕尺寸获取最优分辨率
    
    Args:
        screen_width: 屏幕宽度
        screen_height: 屏幕高度
        
    Returns:
        dict: 最优分辨率配置
    """
    # 按屏幕大小选择最适合的预设
    for preset_name, config in RESOLUTION_PRESETS.items():
        if (config["width"] <= screen_width * 0.9 and 
            config["height"] <= screen_height * 0.9):
            continue
        else:
            # 找到第一个不适合的，返回前一个
            break
    
    # 如果所有预设都适合，选择最大的
    if screen_width >= 1600 and screen_height >= 900:
        return RESOLUTION_PRESETS["wide"]
    elif screen_width >= 1440 and screen_height >= 900:
        return RESOLUTION_PRESETS["large"]
    elif screen_width >= 1280 and screen_height >= 800:
        return RESOLUTION_PRESETS["standard"]
    else:
        return RESOLUTION_PRESETS["compact"]

def get_layout_for_resolution(width, height):
    """
    根据分辨率调整布局参数
    
    Args:
        width: 窗口宽度
        height: 窗口高度
        
    Returns:
        dict: 调整后的布局参数
    """
    layout = LAYOUT_SETTINGS.copy()
    
    # 根据分辨率调整参数
    if width >= 1440:
        layout["sidebar_width"] = 320
        layout["content_margin"] = 25
    elif width <= 1024:
        layout["sidebar_width"] = 280
        layout["content_margin"] = 15
        layout["panel_spacing"] = 8
    
    return layout

# 导出主要配置
__all__ = [
    'RESOLUTION_PRESETS',
    'DEFAULT_RESOLUTION', 
    'MIN_WIDTH',
    'MIN_HEIGHT',
    'LAYOUT_SETTINGS',
    'FONT_SETTINGS',
    'COLOR_THEME',
    'get_resolution_config',
    'validate_resolution',
    'get_optimal_resolution_for_screen',
    'get_layout_for_resolution'
]
