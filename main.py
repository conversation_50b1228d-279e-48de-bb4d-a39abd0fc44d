#!/usr/bin/env python3
"""
OFE2E - Excel数据映射工具
主程序入口
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtCore import Qt
    from gui.main_window import MainWindow
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装PyQt5:")
    print("pip install PyQt5")
    sys.exit(1)


def main():
    """主函数"""
    # 启用高DPI支持（必须在创建QApplication之前设置）
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    # 创建应用程序
    app = QApplication(sys.argv)

    # 设置应用程序属性
    app.setApplicationName("OFE2E")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("OFE2E Team")

    # 创建主窗口
    window = MainWindow()
    window.show()

    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
