# OFE2E 映射源配置优化方案

## 优化概述

根据用户需求，对OFE2E Excel数据映射工具的"🗝️ 映射源配置"组进行了精确优化，主要包括修改主键列配置格式和新增转换列配置功能。

## 优化内容详解

### 1. 主键列配置修改

#### 1.1 占位符文本更新
**修改前：**
```
例如: 序号[云序号], 产品代码[标准代码]
```

**修改后：**
```
例如: 接口标识
```

#### 1.2 工具提示说明更新
**修改前：**
```
映射表的主键列，格式：原列名[新列名]
```

**修改后：**
```
映射表中用于匹配的主键列名
```

#### 1.3 验证逻辑调整
**新的验证规则：**
- ✅ 只接受简单的列名（如：`接口标识`、`产品代码`、`ID`）
- ❌ 不接受方括号格式（如：`序号[云序号]`、`[接口标识]`）
- ❌ 不接受空值

**验证实现：**
```python
def validate_mapping_key_format(self):
    """验证映射主键列格式（简单列名）"""
    text = self.mapping_key_edit.text()
    
    if text.strip():
        # 主键列只需要列名，不需要重命名格式
        if text.strip() and '[' not in text and ']' not in text:
            # 验证通过
            self.mapping_key_edit.setProperty("valid", "true")
            self.mapping_key_status.setText("✓")
            self.mapping_key_edit.setToolTip(f"主键列: {text.strip()}")
        else:
            # 验证失败
            self.mapping_key_edit.setProperty("valid", "false")
            self.mapping_key_status.setText("✗")
            self.mapping_key_edit.setToolTip("主键列格式错误，只需要列名称，不需要方括号")
```

### 2. 新增转换列配置

#### 2.1 UI组件添加
**新增组件：**
- `QLabel("转换列:")` - 标签
- `QLineEdit()` - 输入框 (`mapping_value_edit`)
- `QLabel("✓")` - 状态指示器 (`mapping_value_status`)

#### 2.2 占位符和提示
**占位符文本：**
```
例如: 云序号[云序号]
```

**工具提示说明：**
```
映射表中用于转换的数据列，格式：原列名[新列名]
```

#### 2.3 格式验证
**支持的格式：**
- ✅ `云序号[云序号]` - 完整格式
- ✅ `产品名称[标准产品名]` - 重命名格式
- ✅ `接口标识` - 仅原列名
- ❌ `[云序号]` - 缺少原列名
- ❌ `云序号[]` - 新列名为空
- ❌ 空值

**验证实现：**
```python
def validate_mapping_value_format(self):
    """验证映射转换列格式"""
    text = self.mapping_value_edit.text()
    is_valid, error_msg, original_name, new_name = ColumnConfigValidator.validate_column_format(text)
    
    if text.strip():
        if is_valid:
            # 验证通过
            self.mapping_value_edit.setProperty("valid", "true")
            self.mapping_value_status.setText("✓")
            self.mapping_value_edit.setToolTip(f"原列名: {original_name}\n新列名: {new_name or '(保持原名)'}")
        else:
            # 验证失败
            self.mapping_value_edit.setProperty("valid", "false")
            self.mapping_value_status.setText("✗")
            self.mapping_value_edit.setToolTip(error_msg)
```

### 3. 界面布局更新

#### 3.1 配置说明更新
**新的配置说明：**
```html
<b>映射源配置：</b><br>
• <b>工作表</b>：映射数据所在的工作表名称<br>
• <b>主键列</b>：映射表中用于匹配的主键列名，如 "接口标识"<br>
• <b>转换列</b>：映射表中用于转换的数据列，格式为 "原列名[新列名]"，如 "云序号[云序号]"
```

#### 3.2 表单布局调整
**新的表单结构：**
```
行0: 工作表    [输入框]    [状态]
行1: 主键列    [输入框]    [状态]
行2: 转换列    [输入框]    [状态]  ← 新增
```

### 4. 配置管理更新

#### 4.1 配置结构扩展
**新的配置字段：**
```json
{
  "mapping_file": "",
  "mapping_sheet": "Sheet1",
  "mapping_key_column": "",
  "mapping_value_column": "",  // 新增字段
  "data_column": "",
  "data_key_column": "",
  // ... 其他字段
}
```

#### 4.2 配置保存和加载
**保存配置：**
```python
def get_config_from_ui(self):
    config = {
        "mapping_key_column": self.mapping_key_edit.text(),
        "mapping_value_column": self.mapping_value_edit.text(),  # 新增
        # ... 其他字段
    }
    return config
```

**加载配置：**
```python
def apply_config_to_ui(self, config):
    self.mapping_key_edit.setText(config.get("mapping_key_column", ""))
    self.mapping_value_edit.setText(config.get("mapping_value_column", ""))  # 新增
    # ... 其他字段
```

#### 4.3 配置验证更新
**新的验证规则：**
```python
def validate_config(self):
    errors = []
    
    if not self.mapping_key_edit.text().strip():
        errors.append("请输入映射主键列配置")
        
    if not self.mapping_value_edit.text().strip():
        errors.append("请输入映射转换列配置")  # 新增验证
    
    return errors
```

## 技术实现亮点

### 1. 差异化验证逻辑
- **主键列**：简单列名验证，不允许方括号
- **转换列**：完整格式验证，支持重命名语法

### 2. 一致的UI设计
- 保持与现有输入框相同的样式和布局
- 统一的状态指示器设计
- 协调的间距和对齐

### 3. 完整的配置管理
- 新字段完全集成到配置系统
- 支持保存、加载、重置
- 向后兼容性保证

### 4. 实时验证反馈
- 输入时即时验证
- 清晰的状态指示（✓/✗）
- 详细的错误提示

## 使用示例

### 完整配置示例
```json
{
  "mapping_file": "mapping.xlsx",
  "mapping_sheet": "映射表",
  "mapping_key_column": "接口标识",
  "mapping_value_column": "云序号[云序号]",
  "data_column": "产品名称[标准产品名]",
  "data_key_column": "产品代码"
}
```

### 用户操作流程
1. **选择映射文件** - 选择包含映射关系的Excel文件
2. **输入工作表名** - 如：`映射表`
3. **输入主键列** - 如：`接口标识`（简单列名）
4. **输入转换列** - 如：`云序号[云序号]`（支持重命名）
5. **观察验证状态** - 实时查看✓/✗状态指示

## 测试验证

### 验证用例
```bash
# 运行优化测试
python scripts/test_mapping_config_optimization.py
```

**主键列测试用例：**
- ✅ `接口标识` → 有效
- ✅ `产品代码` → 有效
- ❌ `序号[云序号]` → 无效（包含方括号）
- ❌ `[接口标识]` → 无效（包含方括号）

**转换列测试用例：**
- ✅ `云序号[云序号]` → 有效
- ✅ `产品名称[标准产品名]` → 有效
- ✅ `接口标识` → 有效（仅原列名）
- ❌ `[云序号]` → 无效（缺少原列名）
- ❌ `云序号[]` → 无效（新列名为空）

## 总结

本次优化精确实现了用户的所有要求：

1. ✅ **主键列配置修改** - 改为简单列名格式，更新占位符和验证逻辑
2. ✅ **新增转换列配置** - 完整的UI组件、验证和配置管理
3. ✅ **保持界面一致性** - 统一的设计风格和布局规范
4. ✅ **完善配置管理** - 全面的保存、加载和验证支持

优化后的映射源配置更加清晰、易用，为用户提供了更精确的配置选项，同时保持了系统的一致性和可维护性。
