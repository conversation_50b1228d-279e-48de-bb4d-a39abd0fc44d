#!/usr/bin/env python3
"""
端到端测试
使用docs/box/input下的真实文件进行完整流程测试
"""

import sys
import os
import pandas as pd
from pathlib import Path
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.excel_handler import ExcelHandler
from core.mapper import DataMapper
from core.data_processor import DataProcessor, ProcessingConfig


class EndToEndTester:
    """端到端测试器"""

    def __init__(self):
        self.input_dir = Path("docs/box/input")
        self.output_dir = Path("test_output")
        self.excel_handler = ExcelHandler()
        self.data_mapper = DataMapper()
        self.data_processor = DataProcessor()

        # 确保输出目录存在
        self.output_dir.mkdir(exist_ok=True)

    def inspect_input_files(self):
        """检查输入文件结构"""
        print("🔍 检查输入文件结构")
        print("=" * 50)

        input_files = list(self.input_dir.glob("*.xlsx"))

        for file_path in input_files:
            if file_path.name.startswith("~$"):
                continue  # 跳过临时文件

            print(f"\n📁 文件: {file_path.name}")

            # 获取工作表名称
            sheet_names = self.excel_handler.get_worksheet_names(str(file_path))
            print(f"   工作表: {sheet_names}")

            # 检查每个工作表的列
            for sheet_name in sheet_names:
                columns = self.excel_handler.get_column_names(str(file_path), sheet_name)
                print(f"   {sheet_name} 列: {columns}")

                # 如果是小工作表，显示前几行数据
                try:
                    df = pd.read_excel(file_path, sheet_name=sheet_name, nrows=3)
                    if len(df) > 0:
                        print(f"   {sheet_name} 示例数据:")
                        for i, row in df.iterrows():
                            print(f"     行{i+1}: {dict(row)}")
                except Exception as e:
                    print(f"   无法读取 {sheet_name}: {e}")

    def test_data_source_files(self):
        """测试数据源文件"""
        print("\n🧪 测试数据源文件")
        print("=" * 50)

        data_files = [f for f in self.input_dir.glob("数据源文件*.xlsx")]

        for data_file in data_files:
            print(f"\n📊 测试文件: {data_file.name}")

            # 验证文件结构
            is_valid, error_msg = self.excel_handler.validate_data_source_structure(str(data_file))
            print(f"   结构验证: {'✅' if is_valid else '❌'} {error_msg}")

            if is_valid:
                # 提取日期
                success, error_msg, date_period = self.excel_handler.extract_date_from_filter_sheet(str(data_file))
                print(f"   日期提取: {'✅' if success else '❌'} {date_period or error_msg}")

                # 读取表格数据
                try:
                    df = pd.read_excel(data_file, sheet_name="表格")
                    print(f"   数据行数: {len(df)}")
                    print(f"   数据列: {list(df.columns)}")

                    # 显示前几行数据
                    print("   前3行数据:")
                    for i, row in df.head(3).iterrows():
                        print(f"     {dict(row)}")

                except Exception as e:
                    print(f"   读取表格数据失败: {e}")

    def test_mapping_files(self):
        """测试映射文件"""
        print("\n🗺️ 测试映射文件")
        print("=" * 50)

        mapping_files = [f for f in self.input_dir.glob("映射文件*.xlsx")]

        for mapping_file in mapping_files:
            print(f"\n📋 测试文件: {mapping_file.name}")

            # 获取工作表
            sheet_names = self.excel_handler.get_worksheet_names(str(mapping_file))
            print(f"   工作表: {sheet_names}")

            for sheet_name in sheet_names:
                print(f"\n   工作表: {sheet_name}")
                columns = self.excel_handler.get_column_names(str(mapping_file), sheet_name)
                print(f"   列: {columns}")

                try:
                    df = pd.read_excel(mapping_file, sheet_name=sheet_name)
                    print(f"   映射记录数: {len(df)}")

                    # 显示前几行映射数据
                    print("   前5行映射数据:")
                    for i, row in df.head(5).iterrows():
                        print(f"     {dict(row)}")

                except Exception as e:
                    print(f"   读取映射数据失败: {e}")

    def create_test_configurations(self):
        """创建测试配置"""
        print("\n⚙️ 创建测试配置")
        print("=" * 50)

        # 获取文件列表
        data_files = [str(f) for f in self.input_dir.glob("数据源文件*.xlsx") if not f.name.startswith("~$")]
        mapping_files = [str(f) for f in self.input_dir.glob("映射文件*.xlsx") if not f.name.startswith("~$")]

        if not data_files:
            print("❌ 没有找到数据源文件")
            return []

        if not mapping_files:
            print("❌ 没有找到映射文件")
            return []

        # 检查数据源文件的列结构
        data_columns = self.excel_handler.get_column_names(data_files[0], "表格")
        print(f"\n📊 数据源文件列: {data_columns}")

        # 找到可能的主键列（UUID相关）
        possible_key_columns = [col for col in data_columns if 'UUID' in col or 'uuid' in col or '标识' in col]
        if not possible_key_columns:
            print("❌ 在数据源中未找到合适的主键列")
            return []

        data_key_column = possible_key_columns[0]  # 使用第一个找到的
        print(f"   选择数据主键列: {data_key_column}")

        # 检查映射文件的结构来确定配置
        mapping_file = mapping_files[0]
        sheet_names = self.excel_handler.get_worksheet_names(mapping_file)

        configs = []

        # 为每个工作表创建配置
        for sheet_name in sheet_names:
            columns = self.excel_handler.get_column_names(mapping_file, sheet_name)
            print(f"\n📋 工作表 '{sheet_name}' 的列: {columns}")

            if len(columns) >= 2:
                # 智能匹配映射列
                mapping_key_col = None
                mapping_value_col = None

                # 查找与数据主键列匹配的映射列
                for col in columns:
                    if col == data_key_column or '标识' in col or 'UUID' in col:
                        mapping_key_col = col
                        break

                # 如果没找到匹配的，使用第二列作为主键列
                if not mapping_key_col:
                    mapping_key_col = columns[1] if len(columns) > 1 else columns[0]

                # 选择值列（通常是第一列，包含云序号等）
                for col in columns:
                    if col != mapping_key_col and ('序号' in col or '云' in col or col == columns[0]):
                        mapping_value_col = col
                        break

                if not mapping_value_col:
                    mapping_value_col = columns[0]

                config = ProcessingConfig()
                config.data_files = data_files
                config.mapping_file = mapping_file
                config.mapping_sheet = sheet_name
                config.mapping_key_column = mapping_key_col
                config.mapping_value_column = f"{mapping_value_col}[{mapping_value_col}]"
                config.data_column = f"{data_key_column}[{data_key_column}]"  # 使用实际的数据列名
                config.data_key_column = data_key_column  # 使用实际的主键列名
                config.output_directory = str(self.output_dir)

                print(f"   创建配置:")
                print(f"     映射主键列: {config.mapping_key_column}")
                print(f"     映射值列: {config.mapping_value_column}")
                print(f"     数据列: {config.data_column}")
                print(f"     数据主键列: {config.data_key_column}")

                configs.append((f"配置_{sheet_name}", config))

        return configs

    def run_full_processing_test(self, config_name, config):
        """运行完整处理测试"""
        print(f"\n🚀 运行完整处理测试: {config_name}")
        print("=" * 50)

        # 进度回调
        def progress_callback(progress, message):
            print(f"   进度 {progress}%: {message}")

        # 日志回调
        def log_callback(message):
            print(f"   日志: {message}")

        try:
            # 执行处理
            result = self.data_processor.process_data(
                config,
                progress_callback=progress_callback,
                log_callback=log_callback
            )

            # 显示结果
            print(f"\n📊 处理结果:")
            print(f"   成功: {'✅' if result.success else '❌'}")
            print(f"   处理文件: {result.processed_files}/{result.total_files}")
            print(f"   处理记录: {result.mapped_records}/{result.total_records}")
            print(f"   输出文件: {len(result.output_files)}个")
            print(f"   处理时间: {result.processing_time:.2f}秒")

            if result.output_files:
                print(f"   输出文件列表:")
                for output_file in result.output_files:
                    print(f"     - {output_file}")

            if result.errors:
                print(f"   错误信息:")
                for error in result.errors:
                    print(f"     ❌ {error}")

            if result.warnings:
                print(f"   警告信息:")
                for warning in result.warnings:
                    print(f"     ⚠️ {warning}")

            return result

        except Exception as e:
            print(f"❌ 处理过程中发生异常: {e}")
            import traceback
            traceback.print_exc()
            return None

    def verify_output_files(self, result):
        """验证输出文件"""
        if not result or not result.output_files:
            print("❌ 没有输出文件可验证")
            return False

        print(f"\n🔍 验证输出文件")
        print("=" * 50)

        all_valid = True

        for output_file in result.output_files:
            print(f"\n📄 验证文件: {Path(output_file).name}")

            if not Path(output_file).exists():
                print(f"   ❌ 文件不存在")
                all_valid = False
                continue

            try:
                # 读取输出文件
                df = pd.read_excel(output_file, sheet_name='处理结果')
                print(f"   ✅ 文件可读取")
                print(f"   📊 数据行数: {len(df)}")
                print(f"   📋 数据列: {list(df.columns)}")

                # 显示前几行数据
                if len(df) > 0:
                    print("   前3行数据:")
                    for i, row in df.head(3).iterrows():
                        print(f"     {dict(row)}")
                else:
                    print("   ⚠️ 文件为空")

            except Exception as e:
                print(f"   ❌ 读取文件失败: {e}")
                all_valid = False

        return all_valid

    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 OFE2E 端到端测试")
        print("=" * 60)

        try:
            # 1. 检查输入文件
            self.inspect_input_files()

            # 2. 测试数据源文件
            self.test_data_source_files()

            # 3. 测试映射文件
            self.test_mapping_files()

            # 4. 创建测试配置
            configs = self.create_test_configurations()

            if not configs:
                print("❌ 无法创建有效的测试配置")
                return False

            # 5. 运行处理测试
            all_success = True
            results = []

            for config_name, config in configs:
                result = self.run_full_processing_test(config_name, config)
                results.append(result)
                if not result or not result.success:
                    all_success = False

            # 6. 验证输出文件
            for result in results:
                if result:
                    self.verify_output_files(result)

            # 7. 总结
            print(f"\n🎯 测试总结")
            print("=" * 50)
            print(f"   总体结果: {'✅ 成功' if all_success else '❌ 部分失败'}")
            print(f"   测试配置数: {len(configs)}")
            print(f"   成功配置数: {sum(1 for r in results if r and r.success)}")

            return all_success

        except Exception as e:
            print(f"❌ 测试过程中发生异常: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数"""
    tester = EndToEndTester()
    success = tester.run_all_tests()

    print(f"\n{'='*60}")
    if success:
        print("🎉 端到端测试完成！所有测试通过！")
    else:
        print("⚠️ 端到端测试完成，但部分测试失败。")

    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
