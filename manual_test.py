#!/usr/bin/env python3
"""
手动测试脚本 - 不依赖pytest
"""

import sys
import os
from pathlib import Path
import traceback

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        from core.excel_handler import ExcelHandler
        print("✅ ExcelHandler 导入成功")
    except Exception as e:
        print(f"❌ ExcelHandler 导入失败: {e}")
        traceback.print_exc()
        return False
    
    try:
        from core.mapper import DataMapper
        print("✅ DataMapper 导入成功")
    except Exception as e:
        print(f"❌ DataMapper 导入失败: {e}")
        traceback.print_exc()
        return False
    
    try:
        from core.data_processor import DataProcessor, ProcessingConfig, ProcessingResult
        print("✅ DataProcessor 导入成功")
    except Exception as e:
        print(f"❌ DataProcessor 导入失败: {e}")
        traceback.print_exc()
        return False
    
    return True

def test_excel_handler():
    """测试ExcelHandler基本功能"""
    print("\n🔍 测试ExcelHandler...")
    
    try:
        from core.excel_handler import ExcelHandler
        
        handler = ExcelHandler()
        
        # 测试文件验证
        is_valid, error_msg = handler.validate_file("nonexistent.xlsx")
        if not is_valid and "文件不存在" in error_msg:
            print("✅ 文件验证功能正常")
        else:
            print("❌ 文件验证功能异常")
            return False
        
        # 测试支持的格式
        if handler.supported_formats == ['.xlsx', '.xls']:
            print("✅ 支持的文件格式正确")
        else:
            print("❌ 支持的文件格式不正确")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ ExcelHandler 测试失败: {e}")
        traceback.print_exc()
        return False

def test_data_mapper():
    """测试DataMapper基本功能"""
    print("\n🔍 测试DataMapper...")
    
    try:
        from core.mapper import DataMapper
        
        mapper = DataMapper()
        
        # 测试初始化
        if mapper.mapping_dict == {} and mapper.mapping_stats['total_records'] == 0:
            print("✅ DataMapper 初始化正确")
        else:
            print("❌ DataMapper 初始化异常")
            return False
        
        # 测试加载映射
        test_mapping = {"A001": "CLOUD001", "A002": "CLOUD002"}
        mapper.load_mapping(test_mapping)
        
        if len(mapper.mapping_dict) == 2 and mapper.mapping_dict["A001"] == "CLOUD001":
            print("✅ 映射加载功能正常")
        else:
            print("❌ 映射加载功能异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ DataMapper 测试失败: {e}")
        traceback.print_exc()
        return False

def test_processing_config():
    """测试ProcessingConfig"""
    print("\n🔍 测试ProcessingConfig...")
    
    try:
        from core.data_processor import ProcessingConfig
        
        config = ProcessingConfig()
        
        # 测试初始化
        if (config.data_files == [] and 
            config.mapping_file == "" and 
            config.mapping_sheet == "Sheet1"):
            print("✅ ProcessingConfig 初始化正确")
        else:
            print("❌ ProcessingConfig 初始化异常")
            return False
        
        # 测试验证
        is_valid, errors = config.validate()
        if not is_valid and len(errors) > 0:
            print("✅ 配置验证功能正常")
        else:
            print("❌ 配置验证功能异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ ProcessingConfig 测试失败: {e}")
        traceback.print_exc()
        return False

def test_validators():
    """测试验证器"""
    print("\n🔍 测试验证器...")
    
    try:
        from utils.validators import ColumnConfigValidator, WorksheetValidator
        
        # 测试列配置验证器
        is_valid, error_msg, original, new = ColumnConfigValidator.validate_column_format("产品名称[标准产品名]")
        if is_valid and original == "产品名称" and new == "标准产品名":
            print("✅ 列配置验证器正常")
        else:
            print("❌ 列配置验证器异常")
            return False
        
        # 测试工作表验证器
        is_valid, error_msg = WorksheetValidator.validate_worksheet_name("Sheet1")
        if is_valid:
            print("✅ 工作表验证器正常")
        else:
            print("❌ 工作表验证器异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 验证器测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 OFE2E 手动测试")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_excel_handler,
        test_data_mapper,
        test_processing_config,
        test_validators
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ 测试执行异常: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
