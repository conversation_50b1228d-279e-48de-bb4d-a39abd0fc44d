---
description: 
globs: 
alwaysApply: false
---
# Project Structure Overview

This project's main entry point appears to be `[main.py](mdc:main.py)`. 
Alternative run scripts like `[run_improved.py](mdc:run_improved.py)` and `[run_refactored.py](mdc:run_refactored.py)` also exist.

Configuration files are likely located in the `[config/](mdc:config)` directory at the root and potentially within `[src/config/](mdc:src/config)`.

Tests are managed via `[pytest.ini](mdc:pytest.ini)` and test files can be found in the `[tests/](mdc:tests)` directory, with specific GUI tests like `[test_gui.py](mdc:test_gui.py)`.

