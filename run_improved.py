#!/usr/bin/env python3
"""
OFE2E 改进版启动脚本
展示所有优化改进功能
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    """主函数"""
    print("🚀 启动OFE2E改进版...")
    print("✨ 新功能包括:")
    print("  • Python 3.11兼容")
    print("  • 现代化项目管理 (pyproject.toml + UV)")
    print("  • 可执行文件打包支持")
    print("  • 16:10固定窗口比例")
    print("  • 简化的列配置界面")
    print("  • 智能文件名生成")
    print("  • 配置向导和模板")
    print()
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from gui.main_window import MainWindow
        
        # 启用高DPI支持
        if hasattr(Qt, 'AA_EnableHighDpiScaling'):
            QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
            QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("OFE2E 改进版")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("OFE2E Team")
        
        # 创建主窗口
        window = MainWindow()
        
        # 添加欢迎信息
        window.log_message("🎉 欢迎使用OFE2E改进版!")
        window.log_message("✨ 新功能亮点:")
        window.log_message("  • 16:10黄金比例窗口")
        window.log_message("  • 简化的列配置说明")
        window.log_message("  • 智能输出文件名生成")
        window.log_message("  • 配置向导和预设模板")
        window.log_message("  • 现代化项目管理")
        window.log_message("")
        window.log_message("💡 使用提示:")
        window.log_message("  • 点击'配置向导'获取详细指导")
        window.log_message("  • 使用'加载模板'快速配置")
        window.log_message("  • 选择输出目录，文件名自动生成")
        
        # 显示窗口
        window.show()
        
        print("✅ GUI界面已启动")
        print("📖 请查看界面中的日志获取使用提示")
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装所有依赖包:")
        print("pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
