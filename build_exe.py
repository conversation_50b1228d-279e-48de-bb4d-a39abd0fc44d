#!/usr/bin/env python3
"""
OFE2E 打包脚本
使用PyInstaller将源代码打包成可执行文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

# 项目信息
PROJECT_NAME = "OFE2E"
VERSION = "1.0.0"
MAIN_SCRIPT = "main.py"

# 打包配置
BUILD_DIR = "build"
DIST_DIR = "dist"
SPEC_FILE = f"{PROJECT_NAME}.spec"

def check_dependencies():
    """检查依赖是否安装"""
    print("🔍 检查依赖...")
    
    required_packages = [
        "pyinstaller",
        "PyQt5",
        "pandas",
        "openpyxl"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.lower().replace("-", "_"))
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n⚠️ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖已安装")
    return True

def clean_build():
    """清理构建目录"""
    print("🧹 清理构建目录...")
    
    dirs_to_clean = [BUILD_DIR, DIST_DIR, "__pycache__"]
    files_to_clean = [SPEC_FILE]
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✅ 删除目录: {dir_name}")
    
    for file_name in files_to_clean:
        if os.path.exists(file_name):
            os.remove(file_name)
            print(f"✅ 删除文件: {file_name}")

def create_spec_file():
    """创建PyInstaller规格文件"""
    print("📝 创建PyInstaller规格文件...")
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['{MAIN_SCRIPT}'],
    pathex=[],
    binaries=[],
    datas=[
        ('src', 'src'),
        ('config', 'config'),
        ('resources', 'resources'),
        ('docs', 'docs'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'pandas',
        'openpyxl',
        'numpy',
        'xlrd',
        'xlsxwriter',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'scipy',
        'IPython',
        'jupyter',
        'notebook',
        'tkinter',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{PROJECT_NAME}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version='version_info.txt',
    icon='resources/icon.ico' if os.path.exists('resources/icon.ico') else None,
)
'''
    
    with open(SPEC_FILE, 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print(f"✅ 创建规格文件: {SPEC_FILE}")

def create_version_info():
    """创建版本信息文件"""
    print("📋 创建版本信息文件...")
    
    version_info = f'''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1,0,0,0),
    prodvers=(1,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'OFE2E Team'),
        StringStruct(u'FileDescription', u'Excel数据映射工具'),
        StringStruct(u'FileVersion', u'{VERSION}'),
        StringStruct(u'InternalName', u'{PROJECT_NAME}'),
        StringStruct(u'LegalCopyright', u'Copyright © 2025 OFE2E Team'),
        StringStruct(u'OriginalFilename', u'{PROJECT_NAME}.exe'),
        StringStruct(u'ProductName', u'{PROJECT_NAME}'),
        StringStruct(u'ProductVersion', u'{VERSION}')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
'''
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_info)
    
    print("✅ 创建版本信息文件: version_info.txt")

def build_executable():
    """构建可执行文件"""
    print("🔨 开始构建可执行文件...")
    
    # 构建命令
    cmd = [
        "pyinstaller",
        "--clean",
        "--noconfirm",
        SPEC_FILE
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 构建成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def create_portable_package():
    """创建便携版包"""
    print("📦 创建便携版包...")
    
    exe_path = Path(DIST_DIR) / f"{PROJECT_NAME}.exe"
    if not exe_path.exists():
        print(f"❌ 可执行文件不存在: {exe_path}")
        return False
    
    # 创建便携版目录
    portable_dir = Path(DIST_DIR) / f"{PROJECT_NAME}_Portable"
    portable_dir.mkdir(exist_ok=True)
    
    # 复制可执行文件
    shutil.copy2(exe_path, portable_dir / f"{PROJECT_NAME}.exe")
    
    # 复制必要的目录
    dirs_to_copy = ["docs", "config", "resources"]
    for dir_name in dirs_to_copy:
        if os.path.exists(dir_name):
            shutil.copytree(dir_name, portable_dir / dir_name, dirs_exist_ok=True)
    
    # 创建README文件
    readme_content = f"""# {PROJECT_NAME} 便携版

## 使用说明

1. 双击 {PROJECT_NAME}.exe 启动程序
2. 按照界面提示配置数据源和映射文件
3. 点击"开始处理"执行数据映射

## 目录说明

- {PROJECT_NAME}.exe: 主程序
- config/: 配置文件目录
- docs/: 文档和示例文件
- resources/: 资源文件

## 版本信息

版本: {VERSION}
构建时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 技术支持

如有问题请联系开发团队。
"""
    
    with open(portable_dir / "README.txt", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✅ 便携版包创建完成: {portable_dir}")
    return True

def main():
    """主函数"""
    print(f"🚀 {PROJECT_NAME} 打包脚本")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return 1
    
    # 清理构建目录
    clean_build()
    
    # 创建规格文件
    create_spec_file()
    
    # 创建版本信息
    create_version_info()
    
    # 构建可执行文件
    if not build_executable():
        return 1
    
    # 创建便携版包
    if not create_portable_package():
        return 1
    
    print("\n🎉 打包完成!")
    print(f"📁 输出目录: {DIST_DIR}")
    print(f"📦 便携版: {DIST_DIR}/{PROJECT_NAME}_Portable")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
