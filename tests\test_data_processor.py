"""
测试DataProcessor模块
"""

import pytest
from unittest.mock import Mock
from core.data_processor import DataProcessor, ProcessingConfig, ProcessingResult


class TestProcessingConfig:
    """ProcessingConfig测试类"""
    
    def test_init(self):
        """测试初始化"""
        config = ProcessingConfig()
        assert config.data_files == []
        assert config.mapping_file == ""
        assert config.mapping_sheet == "Sheet1"
        assert config.mapping_key_column == ""
        assert config.mapping_value_column == ""
        assert config.data_column == ""
        assert config.data_key_column == ""
        assert config.output_directory == ""
        assert config.include_header is True
    
    def test_validate_empty_config(self):
        """测试验证空配置"""
        config = ProcessingConfig()
        is_valid, errors = config.validate()
        
        assert not is_valid
        assert len(errors) > 0
        assert "未选择数据源文件" in errors
        assert "未选择映射文件" in errors
        assert "未指定映射工作表" in errors
        assert "未配置映射主键列" in errors
        assert "未配置映射转换列" in errors
        assert "未配置数据列" in errors
        assert "未配置数据主键列" in errors
        assert "未选择输出目录" in errors
    
    def test_validate_complete_config(self, processing_config_data):
        """测试验证完整配置"""
        config = ProcessingConfig()
        for key, value in processing_config_data.items():
            setattr(config, key, value)
        
        is_valid, errors = config.validate()
        assert is_valid
        assert len(errors) == 0
    
    def test_validate_invalid_column_format(self):
        """测试验证无效的列格式"""
        config = ProcessingConfig()
        config.data_files = ["test.xlsx"]
        config.mapping_file = "mapping.xlsx"
        config.mapping_sheet = "Sheet1"
        config.mapping_key_column = "key"
        config.mapping_value_column = "value[new_value]"
        config.data_column = "invalid_format["  # 无效格式
        config.data_key_column = "key"
        config.output_directory = "output"
        
        is_valid, errors = config.validate()
        assert not is_valid
        assert any("数据列配置格式错误" in error for error in errors)


class TestProcessingResult:
    """ProcessingResult测试类"""
    
    def test_init(self):
        """测试初始化"""
        result = ProcessingResult()
        assert result.success is False
        assert result.total_files == 0
        assert result.processed_files == 0
        assert result.failed_files == 0
        assert result.total_records == 0
        assert result.mapped_records == 0
        assert result.unmapped_records == 0
        assert result.output_files == []
        assert result.errors == []
        assert result.warnings == []
        assert result.processing_time == 0.0
    
    def test_add_error(self):
        """测试添加错误"""
        result = ProcessingResult()
        result.add_error("测试错误")
        assert "测试错误" in result.errors
    
    def test_add_warning(self):
        """测试添加警告"""
        result = ProcessingResult()
        result.add_warning("测试警告")
        assert "测试警告" in result.warnings
    
    def test_get_summary(self):
        """测试获取摘要"""
        result = ProcessingResult()
        result.total_files = 2
        result.processed_files = 1
        result.failed_files = 1
        result.total_records = 100
        result.mapped_records = 80
        result.unmapped_records = 20
        result.processing_time = 5.5
        result.output_files = ["output1.xlsx"]
        result.add_error("测试错误")
        result.add_warning("测试警告")
        
        summary = result.get_summary()
        assert "总文件数: 2" in summary
        assert "成功处理: 1" in summary
        assert "处理失败: 1" in summary
        assert "总记录数: 100" in summary
        assert "成功映射: 80" in summary
        assert "映射失败: 20" in summary
        assert "处理时间: 5.50秒" in summary
        assert "输出文件: 1个" in summary
        assert "错误: 1个" in summary
        assert "警告: 1个" in summary


class TestDataProcessor:
    """DataProcessor测试类"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.processor = DataProcessor()
    
    def test_init(self):
        """测试初始化"""
        assert self.processor.excel_handler is not None
        assert self.processor.data_mapper is not None
        assert self.processor.is_processing is False
        assert self.processor.should_stop is False
    
    def test_stop_processing(self):
        """测试停止处理"""
        self.processor.stop_processing()
        assert self.processor.should_stop is True
    
    def test_validate_configuration(self, processing_config_data):
        """测试验证配置"""
        config = ProcessingConfig()
        for key, value in processing_config_data.items():
            setattr(config, key, value)
        
        is_valid, errors = self.processor.validate_configuration(config)
        assert is_valid
        assert len(errors) == 0
    
    def test_process_data_invalid_config(self):
        """测试处理无效配置"""
        config = ProcessingConfig()  # 空配置
        
        result = self.processor.process_data(config)
        
        assert not result.success
        assert len(result.errors) > 0
        assert result.processed_files == 0
    
    def test_process_data_with_callbacks(self, processing_config_data):
        """测试带回调的数据处理"""
        config = ProcessingConfig()
        for key, value in processing_config_data.items():
            setattr(config, key, value)
        
        # 创建模拟回调
        progress_callback = Mock()
        log_callback = Mock()
        
        # 由于没有真实文件，这个测试会失败，但我们可以验证回调被调用
        result = self.processor.process_data(config, progress_callback, log_callback)
        
        # 验证回调被调用
        assert progress_callback.called or log_callback.called
        assert not result.success  # 因为文件不存在
    
    @pytest.mark.integration
    def test_process_data_integration(self, sample_data_source_file, sample_mapping_file, temp_dir):
        """集成测试：完整的数据处理流程"""
        config = ProcessingConfig()
        config.data_files = [sample_data_source_file]
        config.mapping_file = sample_mapping_file
        config.mapping_sheet = "Sheet1"
        config.mapping_key_column = "接口标识"
        config.mapping_value_column = "云序号[云序号]"
        config.data_column = "产品名称[标准产品名]"
        config.data_key_column = "产品代码"
        config.output_directory = str(temp_dir / "output")
        
        # 创建输出目录
        (temp_dir / "output").mkdir(exist_ok=True)
        
        # 执行处理
        result = self.processor.process_data(config)
        
        # 验证结果
        assert result is not None
        # 注意：由于测试数据的限制，可能不会完全成功，但应该有处理尝试
    
    def test_preview_processing_invalid_config(self):
        """测试预览无效配置"""
        config = ProcessingConfig()  # 空配置
        
        success, error_msg, preview_info = self.processor.preview_processing(config)
        
        assert not success
        assert error_msg != ""
        assert preview_info is None
    
    def test_preview_processing_no_files(self, processing_config_data):
        """测试预览没有文件的配置"""
        config = ProcessingConfig()
        for key, value in processing_config_data.items():
            setattr(config, key, value)
        config.data_files = []  # 清空文件列表
        
        success, error_msg, preview_info = self.processor.preview_processing(config)
        
        assert not success
        assert "没有数据文件可预览" in error_msg
        assert preview_info is None
