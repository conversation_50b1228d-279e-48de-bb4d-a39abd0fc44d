"""
数据处理器模块
协调整个数据处理流程的核心逻辑
"""

import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any, Callable
from .excel_handler import ExcelHandler
from .mapper import DataMapper
from utils.validators import ColumnConfigValidator


class ProcessingConfig:
    """处理配置类"""

    def __init__(self):
        self.data_files = []
        self.mapping_file = ""
        self.mapping_sheet = "Sheet1"
        self.mapping_key_column = ""
        self.mapping_value_column = ""
        self.data_columns = []  # 改为列表支持多列
        self.data_key_column = ""
        self.output_directory = ""
        self.merge_output = False

        # 保持向后兼容性
        self._data_column = ""

    @property
    def data_column(self) -> str:
        """向后兼容性：返回第一个数据列配置"""
        if self.data_columns:
            return self.data_columns[0]
        return self._data_column

    @data_column.setter
    def data_column(self, value: str):
        """向后兼容性：设置单个数据列"""
        self._data_column = value
        if value:
            self.data_columns = [value]
        else:
            self.data_columns = []

    def set_data_columns_from_string(self, columns_str: str):
        """
        从字符串设置多个数据列配置

        Args:
            columns_str: 列配置字符串，多个列用分号分隔
                        例如: "UUID[云序号];名称[产品名称];规格"
        """
        if not columns_str or not columns_str.strip():
            self.data_columns = []
            return

        # 按分号分割多个列配置
        column_configs = [col.strip() for col in columns_str.split(';') if col.strip()]
        self.data_columns = column_configs

        # 向后兼容：设置第一个列为data_column
        if column_configs:
            self._data_column = column_configs[0]

    def get_data_columns_string(self) -> str:
        """
        获取数据列配置的字符串表示

        Returns:
            str: 用分号分隔的列配置字符串
        """
        return ';'.join(self.data_columns) if self.data_columns else self._data_column

    def validate(self) -> Tuple[bool, List[str]]:
        """
        验证配置完整性

        Returns:
            Tuple[bool, List[str]]: (是否有效, 错误信息列表)
        """
        errors = []

        if not self.data_files:
            errors.append("未选择数据源文件")

        if not self.mapping_file:
            errors.append("未选择映射文件")

        if not self.mapping_sheet:
            errors.append("未指定映射工作表")

        if not self.mapping_key_column:
            errors.append("未配置映射主键列")

        if not self.mapping_value_column:
            errors.append("未配置映射转换列")

        # 验证数据列配置（支持多列）
        if not self.data_columns and not self.data_column:
            errors.append("未配置数据列")

        # 验证数据列格式
        columns_to_validate = self.data_columns if self.data_columns else ([self.data_column] if self.data_column else [])
        for i, col_config in enumerate(columns_to_validate):
            if col_config:
                is_valid, error_msg, _, _, _, _ = ColumnConfigValidator.validate_column_format(col_config)
                if not is_valid:
                    errors.append(f"数据列{i+1}配置格式错误: {error_msg}")

        if not self.data_key_column:
            errors.append("未配置数据主键列")

        if not self.output_directory:
            errors.append("未选择输出目录")

        if self.mapping_value_column:
            is_valid, error_msg, _, _, _, _ = ColumnConfigValidator.validate_column_format(self.mapping_value_column)
            if not is_valid:
                errors.append(f"映射转换列配置格式错误: {error_msg}")

        return len(errors) == 0, errors


class ProcessingResult:
    """处理结果类"""

    def __init__(self):
        self.success = False
        self.total_files = 0
        self.processed_files = 0
        self.failed_files = 0
        self.total_records = 0
        self.mapped_records = 0
        self.unmapped_records = 0
        self.output_files = []
        self.errors = []
        self.warnings = []
        self.processing_time = 0.0

    def add_error(self, error: str):
        """添加错误信息"""
        self.errors.append(error)

    def add_warning(self, warning: str):
        """添加警告信息"""
        self.warnings.append(warning)

    def get_summary(self) -> str:
        """获取处理结果摘要"""
        summary = f"""
处理完成！
总文件数: {self.total_files}
成功处理: {self.processed_files}
处理失败: {self.failed_files}
总记录数: {self.total_records}
成功映射: {self.mapped_records}
映射失败: {self.unmapped_records}
处理时间: {self.processing_time:.2f}秒
输出文件: {len(self.output_files)}个
        """.strip()

        if self.errors:
            summary += f"\n错误: {len(self.errors)}个"

        if self.warnings:
            summary += f"\n警告: {len(self.warnings)}个"

        return summary


class DataProcessor:
    """数据处理器"""

    def __init__(self):
        """初始化数据处理器"""
        self.excel_handler = ExcelHandler()
        self.data_mapper = DataMapper()
        self.is_processing = False
        self.should_stop = False

    def process_data(self, config: ProcessingConfig,
                    progress_callback: Optional[Callable[[int, str], None]] = None,
                    log_callback: Optional[Callable[[str], None]] = None) -> ProcessingResult:
        """
        执行数据处理

        Args:
            config: 处理配置
            progress_callback: 进度回调函数 (progress, message)
            log_callback: 日志回调函数

        Returns:
            ProcessingResult: 处理结果
        """
        start_time = datetime.now()
        result = ProcessingResult()
        result.total_files = len(config.data_files)

        self.is_processing = True
        self.should_stop = False

        try:
            # 验证配置
            is_valid, errors = config.validate()
            if not is_valid:
                for error in errors:
                    result.add_error(error)
                result.success = False
                return result

            if log_callback:
                log_callback("开始数据处理...")

            # 第一步：加载映射数据
            if progress_callback:
                progress_callback(10, "加载映射数据...")

            success, error_msg, mapping_dict = self._load_mapping_data(config)
            if not success:
                result.add_error(f"加载映射数据失败: {error_msg}")
                result.success = False
                return result

            self.data_mapper.load_mapping(mapping_dict)

            if log_callback:
                log_callback(f"成功加载映射数据，包含 {len(mapping_dict)} 个映射关系")

            # 第二步：处理每个数据源文件
            if config.merge_output:
                # 合并输出模式
                result = self._process_files_merged(config, progress_callback, log_callback, result)
            else:
                # 分别输出模式
                result = self._process_files_separately(config, progress_callback, log_callback, result)

            # 完成处理
            if progress_callback:
                progress_callback(100, "处理完成")

            result.success = result.processed_files > 0

        except Exception as e:
            result.add_error(f"处理过程中发生异常: {str(e)}")
            result.success = False

        finally:
            self.is_processing = False
            end_time = datetime.now()
            result.processing_time = (end_time - start_time).total_seconds()

        return result

    def stop_processing(self):
        """停止处理"""
        self.should_stop = True

    def _load_mapping_data(self, config: ProcessingConfig) -> Tuple[bool, str, Optional[Dict]]:
        """加载映射数据"""
        try:
            # 验证映射文件
            is_valid, error_msg = self.excel_handler.validate_file(config.mapping_file)
            if not is_valid:
                return False, error_msg, None

            # 解析映射转换列配置
            is_valid, error_msg, original_value_name, new_value_name, _, _ = ColumnConfigValidator.validate_column_format(config.mapping_value_column)
            if not is_valid:
                return False, f"映射转换列配置错误: {error_msg}", None

            # 读取映射数据
            success, error_msg, mapping_dict = self.excel_handler.read_mapping_data(
                config.mapping_file,
                config.mapping_sheet,
                config.mapping_key_column,
                original_value_name
            )

            return success, error_msg, mapping_dict

        except Exception as e:
            return False, f"加载映射数据异常: {str(e)}", None

    def _process_files_separately(self, config: ProcessingConfig, progress_callback: Optional[Callable],
                                log_callback: Optional[Callable], result: ProcessingResult) -> ProcessingResult:
        """分别处理每个文件，生成独立的输出文件"""
        for file_index, data_file in enumerate(config.data_files):
            if self.should_stop:
                if log_callback:
                    log_callback("用户取消处理")
                break

            file_progress_base = 20 + (file_index * 70 // len(config.data_files))

            if progress_callback:
                progress_callback(file_progress_base, f"处理文件 {file_index + 1}/{len(config.data_files)}")

            if log_callback:
                log_callback(f"处理文件: {Path(data_file).name}")

            # 处理单个文件
            file_success, file_result = self._process_single_file(
                data_file, config, progress_callback, log_callback, file_progress_base
            )

            if file_success:
                result.processed_files += 1
                result.total_records += file_result['total_records']
                result.mapped_records += file_result['mapped_records']
                result.unmapped_records += file_result['unmapped_records']
                result.output_files.append(file_result['output_file'])

                if log_callback:
                    log_callback(f"文件处理完成: {file_result['output_file']}")
            else:
                result.failed_files += 1
                result.add_error(f"文件 {Path(data_file).name} 处理失败: {file_result.get('error', '未知错误')}")

        return result

    def _process_files_merged(self, config: ProcessingConfig, progress_callback: Optional[Callable],
                            log_callback: Optional[Callable], result: ProcessingResult) -> ProcessingResult:
        """合并处理所有文件，生成单个输出文件"""
        import pandas as pd

        all_mapped_data = []  # 存储所有处理后的数据

        for file_index, data_file in enumerate(config.data_files):
            if self.should_stop:
                if log_callback:
                    log_callback("用户取消处理")
                break

            file_progress_base = 20 + (file_index * 60 // len(config.data_files))

            if progress_callback:
                progress_callback(file_progress_base, f"处理文件 {file_index + 1}/{len(config.data_files)} (合并模式)")

            if log_callback:
                log_callback(f"处理文件: {Path(data_file).name}")

            # 处理单个文件但不保存，只获取处理后的数据
            file_success, file_result = self._process_single_file_for_merge(
                data_file, config, progress_callback, log_callback, file_progress_base
            )

            if file_success:
                result.processed_files += 1
                result.total_records += file_result['total_records']
                result.mapped_records += file_result['mapped_records']
                result.unmapped_records += file_result['unmapped_records']

                # 收集处理后的数据
                all_mapped_data.append(file_result['mapped_data'])

                if log_callback:
                    log_callback(f"文件处理完成: {Path(data_file).name}")
            else:
                result.failed_files += 1
                result.add_error(f"文件 {Path(data_file).name} 处理失败: {file_result.get('error', '未知错误')}")

        # 合并所有数据并保存
        if all_mapped_data:
            if progress_callback:
                progress_callback(85, "合并数据...")

            if log_callback:
                log_callback("合并所有处理结果...")

            try:
                # 合并所有DataFrame
                merged_df = pd.concat(all_mapped_data, ignore_index=True)

                # 生成合并输出文件名
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_filename = f"合并处理结果_{timestamp}.xlsx"
                output_path = Path(config.output_directory) / output_filename

                if progress_callback:
                    progress_callback(90, "保存合并文件...")

                # 写入合并文件
                success, error_msg = self.excel_handler.write_output_file(merged_df, str(output_path))
                if success:
                    result.output_files.append(str(output_path))
                    if log_callback:
                        log_callback(f"合并文件保存完成: {output_path}")
                else:
                    result.add_error(f"保存合并文件失败: {error_msg}")

            except Exception as e:
                result.add_error(f"合并数据失败: {str(e)}")

        return result

    def _process_single_file_for_merge(self, data_file: str, config: ProcessingConfig,
                                     progress_callback: Optional[Callable], log_callback: Optional[Callable],
                                     base_progress: int) -> Tuple[bool, Dict[str, Any]]:
        """处理单个文件用于合并（不保存文件，只返回处理后的数据）"""
        try:
            # 提取日期信息
            if progress_callback:
                progress_callback(base_progress + 5, "提取日期信息...")

            success, error_msg, date_period = self.excel_handler.extract_date_from_filter_sheet(data_file)
            if not success:
                return False, {'error': error_msg}

            # 获取要处理的列配置
            columns_to_process = config.data_columns if config.data_columns else [config.data_column] if config.data_column else []
            if not columns_to_process:
                return False, {'error': "未配置数据列"}

            # 解析列配置以获取原始列名
            original_data_names = []
            for col_config in columns_to_process:
                is_valid, error_msg, original_name, _, _, _ = ColumnConfigValidator.validate_column_format(col_config)
                if not is_valid:
                    return False, {'error': f"数据列配置错误: {error_msg}"}
                original_data_names.append(original_name)

            # 读取数据源文件
            if progress_callback:
                progress_callback(base_progress + 10, "读取数据源...")

            success, error_msg, data_df = self.excel_handler.read_data_source(
                data_file, original_data_names, config.data_key_column
            )
            if not success:
                return False, {'error': error_msg}

            # 执行数据映射
            def mapping_progress(progress):
                if progress_callback:
                    progress_callback(base_progress + 10 + int(progress * 0.5), f"映射数据... {progress}%")

            success, error_msg, mapped_df = self.data_mapper.map_data(
                data_df,
                config.data_key_column,
                columns_to_process,  # 传入完整的列配置（支持多列）
                config.mapping_value_column,
                date_period,
                mapping_progress
            )

            if not success:
                return False, {'error': error_msg}

            # 获取映射统计信息
            stats = self.data_mapper.get_mapping_statistics()

            return True, {
                'mapped_data': mapped_df,  # 返回处理后的数据而不是文件路径
                'total_records': stats['total_records'],
                'mapped_records': stats['mapped_records'],
                'unmapped_records': stats['unmapped_records'],
                'date_period': date_period
            }

        except Exception as e:
            return False, {'error': f"处理文件异常: {str(e)}"}

    def _process_single_file(self, data_file: str, config: ProcessingConfig,
                            progress_callback: Optional[Callable], log_callback: Optional[Callable],
                            base_progress: int) -> Tuple[bool, Dict[str, Any]]:
        """
        处理单个数据源文件

        Args:
            data_file: 数据文件路径
            config: 处理配置
            progress_callback: 进度回调
            log_callback: 日志回调
            base_progress: 基础进度值

        Returns:
            Tuple[bool, Dict]: (是否成功, 处理结果)
        """
        try:
            # 验证文件结构
            is_valid, error_msg = self.excel_handler.validate_data_source_structure(data_file)
            if not is_valid:
                return False, {'error': error_msg}

            # 提取日期信息
            success, error_msg, date_period = self.excel_handler.extract_date_from_filter_sheet(data_file)
            if not success:
                if log_callback:
                    log_callback(f"警告: 无法提取日期信息，使用默认日期 - {error_msg}")
                date_period = datetime.now().strftime("%Y-%m-%d")

            # 解析数据列配置（支持多列）
            columns_to_process = config.data_columns if config.data_columns else ([config.data_column] if config.data_column else [])
            original_data_names = []

            for col_config in columns_to_process:
                is_valid, error_msg, original_name, _, _, _ = ColumnConfigValidator.validate_column_format(col_config)
                if not is_valid:
                    return False, {'error': f"数据列配置错误: {error_msg}"}
                original_data_names.append(original_name)

            # 读取表格数据
            success, error_msg, data_df = self.excel_handler.read_data_from_table_sheet(
                data_file,
                original_data_names,  # 数据列列表（支持多列）
                config.data_key_column  # 主键列
            )

            if not success:
                return False, {'error': error_msg}

            if progress_callback:
                progress_callback(base_progress + 10, "执行数据映射...")

            # 执行数据映射
            def mapping_progress(progress):
                if progress_callback:
                    progress_callback(base_progress + 10 + int(progress * 0.5), f"映射数据... {progress}%")

            success, error_msg, mapped_df = self.data_mapper.map_data(
                data_df,
                config.data_key_column,
                columns_to_process,  # 传入完整的列配置（支持多列）
                config.mapping_value_column,
                date_period,
                mapping_progress
            )

            if not success:
                return False, {'error': error_msg}

            if progress_callback:
                progress_callback(base_progress + 60, "保存输出文件...")

            # 生成输出文件名
            input_filename = Path(data_file).stem
            output_filename = f"{input_filename}_处理结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            output_path = Path(config.output_directory) / output_filename

            # 写入输出文件
            success, error_msg = self.excel_handler.write_output_file(mapped_df, str(output_path))
            if not success:
                return False, {'error': error_msg}

            # 获取映射统计信息
            stats = self.data_mapper.get_mapping_statistics()

            return True, {
                'output_file': str(output_path),
                'total_records': stats['total_records'],
                'mapped_records': stats['mapped_records'],
                'unmapped_records': stats['unmapped_records'],
                'date_period': date_period
            }

        except Exception as e:
            return False, {'error': f"处理文件异常: {str(e)}"}

    def validate_configuration(self, config: ProcessingConfig) -> Tuple[bool, List[str]]:
        """
        验证处理配置

        Args:
            config: 处理配置

        Returns:
            Tuple[bool, List[str]]: (是否有效, 错误信息列表)
        """
        return config.validate()

    def preview_processing(self, config: ProcessingConfig) -> Tuple[bool, str, Optional[Dict]]:
        """
        预览处理结果（不实际执行处理）

        Args:
            config: 处理配置

        Returns:
            Tuple[bool, str, Optional[Dict]]: (是否成功, 错误信息, 预览信息)
        """
        try:
            # 验证配置
            is_valid, errors = config.validate()
            if not is_valid:
                return False, "; ".join(errors), None

            # 加载映射数据
            success, error_msg, mapping_dict = self._load_mapping_data(config)
            if not success:
                return False, error_msg, None

            self.data_mapper.load_mapping(mapping_dict)

            # 预览第一个数据文件
            if config.data_files:
                first_file = config.data_files[0]

                # 验证文件结构
                is_valid, error_msg = self.excel_handler.validate_data_source_structure(first_file)
                if not is_valid:
                    return False, error_msg, None

                # 解析数据列配置（支持多列）
                columns_to_process = config.data_columns if config.data_columns else ([config.data_column] if config.data_column else [])
                original_data_names = []

                for col_config in columns_to_process:
                    is_valid, error_msg, original_name, _, _, _ = ColumnConfigValidator.validate_column_format(col_config)
                    if not is_valid:
                        return False, f"数据列配置错误: {error_msg}", None
                    original_data_names.append(original_name)

                # 读取少量数据进行预览
                success, error_msg, data_df = self.excel_handler.read_data_from_table_sheet(
                    first_file,
                    original_data_names,
                    config.data_key_column
                )

                if not success:
                    return False, error_msg, None

                # 计算映射覆盖率
                coverage_rate, uncovered_keys = self.data_mapper.validate_mapping_coverage(
                    data_df, config.data_key_column
                )

                # 生成预览信息
                preview_info = {
                    'mapping_count': len(mapping_dict),
                    'data_records': len(data_df),
                    'coverage_rate': coverage_rate,
                    'uncovered_count': len(uncovered_keys),
                    'sample_uncovered_keys': uncovered_keys[:10] if uncovered_keys else [],
                    'preview_mapping': self.data_mapper.preview_mapping_result(data_df, config.data_key_column, 5)
                }

                return True, "", preview_info

            return False, "没有数据文件可预览", None

        except Exception as e:
            return False, f"预览失败: {str(e)}", None
