"""
数据处理器模块
协调整个数据处理流程的核心逻辑
"""

import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any, Callable
from .excel_handler import ExcelHandler
from .mapper import DataMapper
from utils.validators import ColumnConfigValidator


class ProcessingConfig:
    """处理配置类"""

    def __init__(self):
        self.data_files = []
        self.mapping_file = ""
        self.mapping_sheet = "Sheet1"
        self.mapping_key_column = ""
        self.mapping_value_column = ""
        self.data_column = ""
        self.data_key_column = ""
        self.output_directory = ""
        self.include_header = True

    def validate(self) -> Tuple[bool, List[str]]:
        """
        验证配置完整性

        Returns:
            Tuple[bool, List[str]]: (是否有效, 错误信息列表)
        """
        errors = []

        if not self.data_files:
            errors.append("未选择数据源文件")

        if not self.mapping_file:
            errors.append("未选择映射文件")

        if not self.mapping_sheet:
            errors.append("未指定映射工作表")

        if not self.mapping_key_column:
            errors.append("未配置映射主键列")

        if not self.mapping_value_column:
            errors.append("未配置映射转换列")

        if not self.data_column:
            errors.append("未配置数据列")

        if not self.data_key_column:
            errors.append("未配置数据主键列")

        if not self.output_directory:
            errors.append("未选择输出目录")

        # 验证列配置格式
        if self.data_column:
            is_valid, error_msg, _, _ = ColumnConfigValidator.validate_column_format(self.data_column)
            if not is_valid:
                errors.append(f"数据列配置格式错误: {error_msg}")

        if self.mapping_value_column:
            is_valid, error_msg, _, _ = ColumnConfigValidator.validate_column_format(self.mapping_value_column)
            if not is_valid:
                errors.append(f"映射转换列配置格式错误: {error_msg}")

        return len(errors) == 0, errors


class ProcessingResult:
    """处理结果类"""

    def __init__(self):
        self.success = False
        self.total_files = 0
        self.processed_files = 0
        self.failed_files = 0
        self.total_records = 0
        self.mapped_records = 0
        self.unmapped_records = 0
        self.output_files = []
        self.errors = []
        self.warnings = []
        self.processing_time = 0.0

    def add_error(self, error: str):
        """添加错误信息"""
        self.errors.append(error)

    def add_warning(self, warning: str):
        """添加警告信息"""
        self.warnings.append(warning)

    def get_summary(self) -> str:
        """获取处理结果摘要"""
        summary = f"""
处理完成！
总文件数: {self.total_files}
成功处理: {self.processed_files}
处理失败: {self.failed_files}
总记录数: {self.total_records}
成功映射: {self.mapped_records}
映射失败: {self.unmapped_records}
处理时间: {self.processing_time:.2f}秒
输出文件: {len(self.output_files)}个
        """.strip()

        if self.errors:
            summary += f"\n错误: {len(self.errors)}个"

        if self.warnings:
            summary += f"\n警告: {len(self.warnings)}个"

        return summary


class DataProcessor:
    """数据处理器"""

    def __init__(self):
        """初始化数据处理器"""
        self.excel_handler = ExcelHandler()
        self.data_mapper = DataMapper()
        self.is_processing = False
        self.should_stop = False

    def process_data(self, config: ProcessingConfig,
                    progress_callback: Optional[Callable[[int, str], None]] = None,
                    log_callback: Optional[Callable[[str], None]] = None) -> ProcessingResult:
        """
        执行数据处理

        Args:
            config: 处理配置
            progress_callback: 进度回调函数 (progress, message)
            log_callback: 日志回调函数

        Returns:
            ProcessingResult: 处理结果
        """
        start_time = datetime.now()
        result = ProcessingResult()
        result.total_files = len(config.data_files)

        self.is_processing = True
        self.should_stop = False

        try:
            # 验证配置
            is_valid, errors = config.validate()
            if not is_valid:
                for error in errors:
                    result.add_error(error)
                result.success = False
                return result

            if log_callback:
                log_callback("开始数据处理...")

            # 第一步：加载映射数据
            if progress_callback:
                progress_callback(10, "加载映射数据...")

            success, error_msg, mapping_dict = self._load_mapping_data(config)
            if not success:
                result.add_error(f"加载映射数据失败: {error_msg}")
                result.success = False
                return result

            self.data_mapper.load_mapping(mapping_dict)

            if log_callback:
                log_callback(f"成功加载映射数据，包含 {len(mapping_dict)} 个映射关系")

            # 第二步：处理每个数据源文件
            for file_index, data_file in enumerate(config.data_files):
                if self.should_stop:
                    if log_callback:
                        log_callback("用户取消处理")
                    break

                file_progress_base = 20 + (file_index * 70 // len(config.data_files))

                if progress_callback:
                    progress_callback(file_progress_base, f"处理文件 {file_index + 1}/{len(config.data_files)}")

                if log_callback:
                    log_callback(f"处理文件: {Path(data_file).name}")

                # 处理单个文件
                file_success, file_result = self._process_single_file(
                    data_file, config, progress_callback, log_callback, file_progress_base
                )

                if file_success:
                    result.processed_files += 1
                    result.total_records += file_result['total_records']
                    result.mapped_records += file_result['mapped_records']
                    result.unmapped_records += file_result['unmapped_records']
                    result.output_files.append(file_result['output_file'])

                    if log_callback:
                        log_callback(f"文件处理完成: {file_result['output_file']}")
                else:
                    result.failed_files += 1
                    result.add_error(f"文件 {Path(data_file).name} 处理失败: {file_result.get('error', '未知错误')}")

            # 完成处理
            if progress_callback:
                progress_callback(100, "处理完成")

            result.success = result.processed_files > 0

        except Exception as e:
            result.add_error(f"处理过程中发生异常: {str(e)}")
            result.success = False

        finally:
            self.is_processing = False
            end_time = datetime.now()
            result.processing_time = (end_time - start_time).total_seconds()

        return result

    def stop_processing(self):
        """停止处理"""
        self.should_stop = True

    def _load_mapping_data(self, config: ProcessingConfig) -> Tuple[bool, str, Optional[Dict]]:
        """加载映射数据"""
        try:
            # 验证映射文件
            is_valid, error_msg = self.excel_handler.validate_file(config.mapping_file)
            if not is_valid:
                return False, error_msg, None

            # 解析映射转换列配置
            is_valid, error_msg, original_value_name, new_value_name = ColumnConfigValidator.validate_column_format(config.mapping_value_column)
            if not is_valid:
                return False, f"映射转换列配置错误: {error_msg}", None

            # 读取映射数据
            success, error_msg, mapping_dict = self.excel_handler.read_mapping_data(
                config.mapping_file,
                config.mapping_sheet,
                config.mapping_key_column,
                original_value_name
            )

            return success, error_msg, mapping_dict

        except Exception as e:
            return False, f"加载映射数据异常: {str(e)}", None

    def _process_single_file(self, data_file: str, config: ProcessingConfig,
                            progress_callback: Optional[Callable], log_callback: Optional[Callable],
                            base_progress: int) -> Tuple[bool, Dict[str, Any]]:
        """
        处理单个数据源文件

        Args:
            data_file: 数据文件路径
            config: 处理配置
            progress_callback: 进度回调
            log_callback: 日志回调
            base_progress: 基础进度值

        Returns:
            Tuple[bool, Dict]: (是否成功, 处理结果)
        """
        try:
            # 验证文件结构
            is_valid, error_msg = self.excel_handler.validate_data_source_structure(data_file)
            if not is_valid:
                return False, {'error': error_msg}

            # 提取日期信息
            success, error_msg, date_period = self.excel_handler.extract_date_from_filter_sheet(data_file)
            if not success:
                if log_callback:
                    log_callback(f"警告: 无法提取日期信息，使用默认日期 - {error_msg}")
                date_period = datetime.now().strftime("%Y-%m-%d")

            # 解析数据列配置
            is_valid, error_msg, original_data_name, _ = ColumnConfigValidator.validate_column_format(config.data_column)
            if not is_valid:
                return False, {'error': f"数据列配置错误: {error_msg}"}

            # 读取表格数据
            success, error_msg, data_df = self.excel_handler.read_data_from_table_sheet(
                data_file,
                [original_data_name],  # 数据列列表
                config.data_key_column  # 主键列
            )

            if not success:
                return False, {'error': error_msg}

            if progress_callback:
                progress_callback(base_progress + 10, "执行数据映射...")

            # 执行数据映射
            def mapping_progress(progress):
                if progress_callback:
                    progress_callback(base_progress + 10 + int(progress * 0.5), f"映射数据... {progress}%")

            success, error_msg, mapped_df = self.data_mapper.map_data(
                data_df,
                config.data_key_column,
                [config.data_column],  # 传入完整的列配置
                config.mapping_value_column,
                date_period,
                mapping_progress
            )

            if not success:
                return False, {'error': error_msg}

            if progress_callback:
                progress_callback(base_progress + 60, "保存输出文件...")

            # 生成输出文件名
            input_filename = Path(data_file).stem
            output_filename = f"{input_filename}_处理结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            output_path = Path(config.output_directory) / output_filename

            # 写入输出文件
            success, error_msg = self.excel_handler.write_output_file(mapped_df, str(output_path))
            if not success:
                return False, {'error': error_msg}

            # 获取映射统计信息
            stats = self.data_mapper.get_mapping_statistics()

            return True, {
                'output_file': str(output_path),
                'total_records': stats['total_records'],
                'mapped_records': stats['mapped_records'],
                'unmapped_records': stats['unmapped_records'],
                'date_period': date_period
            }

        except Exception as e:
            return False, {'error': f"处理文件异常: {str(e)}"}

    def validate_configuration(self, config: ProcessingConfig) -> Tuple[bool, List[str]]:
        """
        验证处理配置

        Args:
            config: 处理配置

        Returns:
            Tuple[bool, List[str]]: (是否有效, 错误信息列表)
        """
        return config.validate()

    def preview_processing(self, config: ProcessingConfig) -> Tuple[bool, str, Optional[Dict]]:
        """
        预览处理结果（不实际执行处理）

        Args:
            config: 处理配置

        Returns:
            Tuple[bool, str, Optional[Dict]]: (是否成功, 错误信息, 预览信息)
        """
        try:
            # 验证配置
            is_valid, errors = config.validate()
            if not is_valid:
                return False, "; ".join(errors), None

            # 加载映射数据
            success, error_msg, mapping_dict = self._load_mapping_data(config)
            if not success:
                return False, error_msg, None

            self.data_mapper.load_mapping(mapping_dict)

            # 预览第一个数据文件
            if config.data_files:
                first_file = config.data_files[0]

                # 验证文件结构
                is_valid, error_msg = self.excel_handler.validate_data_source_structure(first_file)
                if not is_valid:
                    return False, error_msg, None

                # 解析数据列配置
                is_valid, error_msg, original_data_name, _ = ColumnConfigValidator.validate_column_format(config.data_column)
                if not is_valid:
                    return False, f"数据列配置错误: {error_msg}", None

                # 读取少量数据进行预览
                success, error_msg, data_df = self.excel_handler.read_data_from_table_sheet(
                    first_file,
                    [original_data_name],
                    config.data_key_column
                )

                if not success:
                    return False, error_msg, None

                # 计算映射覆盖率
                coverage_rate, uncovered_keys = self.data_mapper.validate_mapping_coverage(
                    data_df, config.data_key_column
                )

                # 生成预览信息
                preview_info = {
                    'mapping_count': len(mapping_dict),
                    'data_records': len(data_df),
                    'coverage_rate': coverage_rate,
                    'uncovered_count': len(uncovered_keys),
                    'sample_uncovered_keys': uncovered_keys[:10] if uncovered_keys else [],
                    'preview_mapping': self.data_mapper.preview_mapping_result(data_df, config.data_key_column, 5)
                }

                return True, "", preview_info

            return False, "没有数据文件可预览", None

        except Exception as e:
            return False, f"预览失败: {str(e)}", None
