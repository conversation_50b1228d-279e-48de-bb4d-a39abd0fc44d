#!/usr/bin/env python3
"""
运行测试脚本
提供多种测试运行选项
"""

import sys
import subprocess
from pathlib import Path


def run_command(cmd, description=""):
    """运行命令并显示结果"""
    if description:
        print(f"\n🔍 {description}")
        print("=" * 50)
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.stdout:
            print(result.stdout)
        
        if result.stderr:
            print("错误输出:", result.stderr)
        
        return result.returncode == 0
    
    except Exception as e:
        print(f"执行命令失败: {e}")
        return False


def check_dependencies():
    """检查测试依赖"""
    print("🔧 检查测试依赖...")
    
    try:
        import pytest
        print(f"✅ pytest 版本: {pytest.__version__}")
    except ImportError:
        print("❌ pytest 未安装")
        print("请运行: pip install pytest")
        return False
    
    try:
        import pandas
        print(f"✅ pandas 版本: {pandas.__version__}")
    except ImportError:
        print("❌ pandas 未安装")
        return False
    
    try:
        import openpyxl
        print(f"✅ openpyxl 版本: {openpyxl.__version__}")
    except ImportError:
        print("❌ openpyxl 未安装")
        return False
    
    return True


def run_all_tests():
    """运行所有测试"""
    return run_command("pytest tests/", "运行所有测试")


def run_unit_tests():
    """运行单元测试"""
    return run_command("pytest tests/ -m 'not integration'", "运行单元测试")


def run_integration_tests():
    """运行集成测试"""
    return run_command("pytest tests/ -m integration", "运行集成测试")


def run_coverage_tests():
    """运行覆盖率测试"""
    print("🔍 运行覆盖率测试")
    print("=" * 50)
    
    # 检查是否安装了coverage
    try:
        import coverage
        print(f"✅ coverage 版本: {coverage.__version__}")
    except ImportError:
        print("❌ coverage 未安装，正在安装...")
        if not run_command("pip install coverage", "安装coverage"):
            return False
    
    # 运行覆盖率测试
    success = run_command(
        "coverage run -m pytest tests/ && coverage report -m",
        "生成覆盖率报告"
    )
    
    if success:
        print("\n📊 生成HTML覆盖率报告...")
        run_command("coverage html", "生成HTML报告")
        print("HTML报告位置: htmlcov/index.html")
    
    return success


def run_specific_module(module_name):
    """运行特定模块的测试"""
    test_file = f"tests/test_{module_name}.py"
    if not Path(test_file).exists():
        print(f"❌ 测试文件不存在: {test_file}")
        return False
    
    return run_command(f"pytest {test_file} -v", f"运行 {module_name} 模块测试")


def run_verbose_tests():
    """运行详细输出的测试"""
    return run_command("pytest tests/ -v -s", "运行详细测试")


def main():
    """主函数"""
    print("🧪 OFE2E 测试运行器")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "check":
            check_dependencies()
        elif command == "all":
            if check_dependencies():
                run_all_tests()
        elif command == "unit":
            if check_dependencies():
                run_unit_tests()
        elif command == "integration":
            if check_dependencies():
                run_integration_tests()
        elif command == "coverage":
            if check_dependencies():
                run_coverage_tests()
        elif command == "verbose":
            if check_dependencies():
                run_verbose_tests()
        elif command in ["excel", "mapper", "processor", "data_processor"]:
            if check_dependencies():
                run_specific_module(command)
        else:
            print(f"❌ 未知命令: {command}")
            show_help()
    else:
        show_help()


def show_help():
    """显示帮助信息"""
    print("""
使用方法:
    python run_tests.py <command>

可用命令:
    check       - 检查测试依赖
    all         - 运行所有测试
    unit        - 运行单元测试
    integration - 运行集成测试
    coverage    - 运行覆盖率测试
    verbose     - 运行详细输出测试
    excel       - 运行Excel处理器测试
    mapper      - 运行数据映射器测试
    processor   - 运行数据处理器测试

示例:
    python run_tests.py check
    python run_tests.py all
    python run_tests.py coverage
    python run_tests.py excel
    """)


if __name__ == "__main__":
    main()
