#!/usr/bin/env python3
"""
测试映射源配置优化功能
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

def test_mapping_config_validation():
    """测试映射配置验证功能"""
    print("🧪 测试映射配置验证功能...")
    
    try:
        from utils.validators import ColumnConfigValidator, WorksheetValidator
        
        # 测试主键列验证（简单列名格式）
        print("\n🔑 测试主键列验证（简单列名格式）:")
        key_test_cases = [
            ("接口标识", True),
            ("产品代码", True),
            ("ID", True),
            ("序号[云序号]", False),  # 不应该包含方括号
            ("[接口标识]", False),   # 不应该包含方括号
            ("", False),            # 空值
        ]
        
        for test_input, expected in key_test_cases:
            # 模拟主键列验证逻辑
            is_valid = (test_input.strip() and 
                       '[' not in test_input and 
                       ']' not in test_input)
            status = "✅" if is_valid == expected else "❌"
            print(f"  {status} '{test_input}' -> {is_valid} (期望: {expected})")
        
        # 测试转换列验证（原列名[新列名]格式）
        print("\n🔄 测试转换列验证（原列名[新列名]格式）:")
        value_test_cases = [
            ("云序号[云序号]", True),
            ("产品名称[标准产品名]", True),
            ("接口标识[标准接口标识]", True),
            ("云序号", True),  # 只有原列名也可以
            ("[云序号]", False),  # 缺少原列名
            ("云序号[]", False),  # 新列名为空
            ("", False),  # 空值
        ]
        
        for test_input, expected in value_test_cases:
            is_valid, error_msg, original, new = ColumnConfigValidator.validate_column_format(test_input)
            status = "✅" if is_valid == expected else "❌"
            print(f"  {status} '{test_input}' -> {is_valid} (原列名: {original}, 新列名: {new})")
        
        print("✅ 映射配置验证测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 映射配置验证测试失败: {e}")
        return False


def test_optimized_mapping_gui():
    """测试优化后的映射源配置GUI"""
    print("\n🧪 测试优化后的映射源配置GUI...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from gui.main_window import MainWindow
        
        # 启用高DPI支持
        if hasattr(Qt, 'AA_EnableHighDpiScaling'):
            QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
            QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("OFE2E 映射配置优化测试")
        app.setApplicationVersion("1.0.0")
        
        # 创建主窗口
        window = MainWindow()
        
        print("✅ 优化后的映射源配置GUI创建成功")
        
        # 验证新字段是否存在
        assert hasattr(window, 'mapping_key_edit'), "映射主键列输入框不存在"
        assert hasattr(window, 'mapping_value_edit'), "映射转换列输入框不存在"
        assert hasattr(window, 'mapping_key_status'), "映射主键列状态指示器不存在"
        assert hasattr(window, 'mapping_value_status'), "映射转换列状态指示器不存在"
        
        print("✅ 所有必需的UI组件都已创建")
        
        # 显示窗口
        window.show()
        
        # 添加测试信息
        window.log_message("🎯 映射源配置优化测试启动")
        window.log_message("✨ 优化内容:")
        window.log_message("  • 主键列改为简单列名格式")
        window.log_message("  • 新增转换列配置")
        window.log_message("  • 实时格式验证")
        window.log_message("  • 更新配置说明")
        window.log_message("")
        window.log_message("🔧 测试示例:")
        window.log_message("  • 主键列: 接口标识")
        window.log_message("  • 转换列: 云序号[云序号]")
        window.log_message("  • 工作表: 映射表")
        
        # 设置测试配置
        window.mapping_sheet_edit.setText("映射表")
        window.mapping_key_edit.setText("接口标识")
        window.mapping_value_edit.setText("云序号[云序号]")
        
        # 验证占位符文本
        key_placeholder = window.mapping_key_edit.placeholderText()
        value_placeholder = window.mapping_value_edit.placeholderText()
        
        print(f"✅ 主键列占位符: '{key_placeholder}'")
        print(f"✅ 转换列占位符: '{value_placeholder}'")
        
        assert "接口标识" in key_placeholder, f"主键列占位符不正确: {key_placeholder}"
        assert "云序号[云序号]" in value_placeholder, f"转换列占位符不正确: {value_placeholder}"
        
        print("✅ 占位符文本验证通过")
        
        # 测试配置保存和加载
        config = window.get_config_from_ui()
        assert "mapping_key_column" in config, "配置中缺少mapping_key_column"
        assert "mapping_value_column" in config, "配置中缺少mapping_value_column"
        
        print("✅ 配置保存和加载功能正常")
        
        print("\n🎉 映射源配置优化测试成功!")
        print("请在界面中测试以下功能:")
        print("1. 主键列输入简单列名（如：接口标识）")
        print("2. 转换列输入格式化列名（如：云序号[云序号]）")
        print("3. 观察实时验证状态")
        print("4. 查看更新的配置说明")
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config_structure():
    """测试配置结构"""
    print("\n🧪 测试配置结构...")
    
    try:
        from config.settings import DEFAULT_CONFIG
        
        # 验证新字段是否在默认配置中
        required_fields = [
            "mapping_key_column",
            "mapping_value_column",
            "data_column",
            "data_key_column"
        ]
        
        print("📋 验证默认配置结构:")
        for field in required_fields:
            if field in DEFAULT_CONFIG:
                print(f"  ✅ {field}: {DEFAULT_CONFIG[field]}")
            else:
                print(f"  ❌ 缺少字段: {field}")
                return False
        
        print("✅ 配置结构验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置结构测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 OFE2E 映射源配置优化测试")
    print("=" * 60)
    
    # 测试映射配置验证
    if not test_mapping_config_validation():
        return
    
    # 测试配置结构
    if not test_config_structure():
        return
    
    print("\n" + "=" * 60)
    
    # 测试优化后的GUI
    test_optimized_mapping_gui()


if __name__ == "__main__":
    main()
