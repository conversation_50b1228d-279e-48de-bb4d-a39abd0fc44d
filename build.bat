@echo off
chcp 65001 >nul
echo 🚀 OFE2E 打包脚本
echo ==================

echo 📋 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装或未添加到PATH
    echo 请安装Python并添加到系统PATH
    pause
    exit /b 1
)

echo ✅ Python 环境正常

echo 📦 检查依赖包...
python -c "import PyQt5" >nul 2>&1
if errorlevel 1 (
    echo ❌ PyQt5 未安装
    echo 正在安装 PyQt5...
    pip install PyQt5
)

python -c "import pandas" >nul 2>&1
if errorlevel 1 (
    echo ❌ pandas 未安装
    echo 正在安装 pandas...
    pip install pandas
)

python -c "import openpyxl" >nul 2>&1
if errorlevel 1 (
    echo ❌ openpyxl 未安装
    echo 正在安装 openpyxl...
    pip install openpyxl
)

python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo ❌ PyInstaller 未安装
    echo 正在安装 PyInstaller...
    pip install pyinstaller
)

echo ✅ 所有依赖包已安装

echo 🔨 开始打包...
python quick_build.py

if exist "dist\OFE2E.exe" (
    echo ✅ 打包成功!
    echo 📁 输出文件: dist\OFE2E.exe
    echo.
    echo 🎉 打包完成! 可执行文件位于 dist 目录
) else (
    echo ❌ 打包失败
)

echo.
pause
