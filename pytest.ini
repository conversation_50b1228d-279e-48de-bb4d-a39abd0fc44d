[tool:pytest]
# pytest配置文件

# 测试目录
testpaths = tests

# 测试文件模式
python_files = test_*.py *_test.py

# 测试类模式
python_classes = Test*

# 测试函数模式
python_functions = test_*

# 标记
markers =
    unit: 单元测试
    integration: 集成测试
    slow: 慢速测试
    excel: Excel相关测试
    mapper: 映射相关测试
    processor: 处理器相关测试

# 输出选项
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes

# 最小版本要求
minversion = 6.0

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
