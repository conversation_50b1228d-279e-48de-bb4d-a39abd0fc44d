#!/usr/bin/env python3
"""
测试重构后的GUI配置逻辑
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

def test_refactored_gui():
    """测试重构后的GUI"""
    print("🧪 测试重构后的GUI配置逻辑...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from gui.main_window import MainWindow
        
        # 启用高DPI支持
        if hasattr(Qt, 'AA_EnableHighDpiScaling'):
            QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
            QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("OFE2E 重构测试")
        app.setApplicationVersion("1.0.0")
        
        # 创建主窗口
        window = MainWindow()
        
        print("✅ GUI重构成功")
        print("✅ 新的配置结构:")
        print("  • 数据源配置包含列配置")
        print("  • 映射源配置简化")
        print("  • 移除独立的列配置组")
        print("  • 支持新的配置格式")
        
        # 显示窗口
        window.show()
        
        # 添加测试日志
        window.log_message("🎉 GUI重构测试启动成功")
        window.log_message("✨ 重构亮点:")
        window.log_message("  • 移除了独立的列配置模块")
        window.log_message("  • 数据源配置集成列配置功能")
        window.log_message("  • 映射源配置简化为文本输入")
        window.log_message("  • 支持新格式：原列名[新列名]")
        window.log_message("  • 实时格式验证和提示")
        window.log_message("")
        window.log_message("💡 测试建议:")
        window.log_message("  • 在数据列输入：产品名称[标准产品名]")
        window.log_message("  • 在主键列输入：产品代码")
        window.log_message("  • 在映射主键列输入：序号[云序号]")
        window.log_message("  • 观察实时验证状态")
        
        # 设置一些示例配置进行测试
        window.data_column_edit.setText("产品名称[标准产品名]")
        window.data_key_column_edit.setText("产品代码")
        window.mapping_key_edit.setText("序号[云序号]")
        window.mapping_sheet_edit.setText("映射表")
        
        print("\n🎉 重构测试成功!")
        print("请在界面中测试以下新功能:")
        print("1. 数据源列配置的实时验证")
        print("2. 映射源配置的格式检查")
        print("3. 新的配置格式支持")
        print("4. 配置保存和加载")
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_validators():
    """测试验证器功能"""
    print("🧪 测试验证器功能...")
    
    try:
        from utils.validators import ColumnConfigValidator, WorksheetValidator
        
        # 测试列配置验证器
        test_cases = [
            ("产品名称[标准产品名]", True),
            ("产品代码", True),
            ("序号[云序号]", True),
            ("[新列名]", False),
            ("原列名[]", False),
            ("", False),
            ("产品名称[标准产品名][额外]", False),
        ]
        
        print("测试列配置验证器:")
        for test_input, expected in test_cases:
            is_valid, error_msg, original, new = ColumnConfigValidator.validate_column_format(test_input)
            status = "✅" if is_valid == expected else "❌"
            print(f"  {status} '{test_input}' -> {is_valid} (原列名: {original}, 新列名: {new})")
        
        # 测试工作表验证器
        worksheet_cases = [
            ("Sheet1", True),
            ("映射表", True),
            ("数据", True),
            ("", False),
            ("包含/的名称", False),
            ("包含*的名称", False),
            ("a" * 32, False),  # 超过31字符
        ]
        
        print("\n测试工作表验证器:")
        for test_input, expected in worksheet_cases:
            is_valid, error_msg = WorksheetValidator.validate_worksheet_name(test_input)
            status = "✅" if is_valid == expected else "❌"
            print(f"  {status} '{test_input}' -> {is_valid}")
        
        print("✅ 验证器测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 验证器测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 OFE2E GUI重构测试")
    print("=" * 50)
    
    # 测试验证器
    if not test_validators():
        return
    
    print()
    
    # 测试重构后的GUI
    test_refactored_gui()


if __name__ == "__main__":
    main()
