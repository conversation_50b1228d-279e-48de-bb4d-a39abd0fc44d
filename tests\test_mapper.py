"""
测试DataMapper模块
"""

import pytest
import pandas as pd
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from core.mapper import DataMapper


class TestDataMapper:
    """DataMapper测试类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.mapper = DataMapper()

    def test_init(self):
        """测试初始化"""
        assert self.mapper.mapping_dict == {}
        assert self.mapper.mapping_stats['total_records'] == 0
        assert self.mapper.mapping_stats['mapped_records'] == 0
        assert self.mapper.mapping_stats['unmapped_records'] == 0
        assert self.mapper.mapping_stats['unmapped_keys'] == []

    def test_load_mapping(self, sample_mapping_dict):
        """测试加载映射字典"""
        self.mapper.load_mapping(sample_mapping_dict)
        assert len(self.mapper.mapping_dict) == 3
        assert self.mapper.mapping_dict["A001"] == "CLOUD001"
        assert self.mapper.mapping_dict["A002"] == "CLOUD002"
        assert self.mapper.mapping_dict["A003"] == "CLOUD003"

    def test_map_data_success(self, sample_mapping_dict, sample_test_data):
        """测试成功的数据映射"""
        self.mapper.load_mapping(sample_mapping_dict)

        success, error_msg, result_df = self.mapper.map_data(
            sample_test_data,
            key_column="产品代码",
            data_columns=["产品名称[标准产品名]"],
            value_column_config="云序号[云序号]",
            date_period="2025-01-15"
        )

        assert success
        assert error_msg == ""
        assert result_df is not None
        assert len(result_df) == 3  # A004没有映射，所以只有3条记录
        assert "周期" in result_df.columns
        assert "云序号" in result_df.columns
        assert "标准产品名" in result_df.columns

        # 验证数据内容
        assert all(result_df["周期"] == "2025-01-15")
        assert "CLOUD001" in result_df["云序号"].values
        assert "CLOUD002" in result_df["云序号"].values
        assert "CLOUD003" in result_df["云序号"].values

    def test_map_data_invalid_value_column_config(self, sample_mapping_dict, sample_test_data):
        """测试无效的值列配置"""
        self.mapper.load_mapping(sample_mapping_dict)

        # 使用一个真正无效的格式（包含无效字符）
        success, error_msg, result_df = self.mapper.map_data(
            sample_test_data,
            key_column="产品代码",
            data_columns=["产品名称"],
            value_column_config="",  # 空字符串是无效的
            date_period="2025-01-15"
        )

        assert not success
        assert "值列配置格式错误" in error_msg
        assert result_df is None

    def test_map_data_no_mappings(self, sample_test_data):
        """测试没有映射的情况"""
        # 不加载任何映射
        success, error_msg, result_df = self.mapper.map_data(
            sample_test_data,
            key_column="产品代码",
            data_columns=["产品名称[标准产品名]"],
            value_column_config="云序号[云序号]",
            date_period="2025-01-15"
        )

        assert not success
        assert "没有成功映射的数据记录" in error_msg
        assert result_df is None

    def test_get_mapping_statistics(self, sample_mapping_dict, sample_test_data):
        """测试获取映射统计信息"""
        self.mapper.load_mapping(sample_mapping_dict)

        # 执行映射
        self.mapper.map_data(
            sample_test_data,
            key_column="产品代码",
            data_columns=["产品名称[标准产品名]"],
            value_column_config="云序号[云序号]",
            date_period="2025-01-15"
        )

        stats = self.mapper.get_mapping_statistics()
        assert stats['total_records'] == 4
        assert stats['mapped_records'] == 3
        assert stats['unmapped_records'] == 1
        assert "A004" in stats['unmapped_keys']

    def test_validate_mapping_coverage(self, sample_mapping_dict, sample_test_data):
        """测试验证映射覆盖率"""
        self.mapper.load_mapping(sample_mapping_dict)

        coverage_rate, uncovered_keys = self.mapper.validate_mapping_coverage(
            sample_test_data, "产品代码"
        )

        assert coverage_rate == 75.0  # 3/4 = 75%
        assert "A004" in uncovered_keys
        assert len(uncovered_keys) == 1

    def test_validate_mapping_coverage_empty_data(self, sample_mapping_dict):
        """测试空数据的映射覆盖率"""
        self.mapper.load_mapping(sample_mapping_dict)

        empty_data = pd.DataFrame({"产品代码": []})
        coverage_rate, uncovered_keys = self.mapper.validate_mapping_coverage(
            empty_data, "产品代码"
        )

        assert coverage_rate == 0.0
        assert uncovered_keys == []

    def test_preview_mapping_result(self, sample_mapping_dict, sample_test_data):
        """测试预览映射结果"""
        self.mapper.load_mapping(sample_mapping_dict)

        preview_results = self.mapper.preview_mapping_result(
            sample_test_data, "产品代码", sample_size=3
        )

        assert len(preview_results) == 3

        # 检查第一个结果
        first_result = preview_results[0]
        assert "key_value" in first_result
        assert "mapped_value" in first_result
        assert "mapping_found" in first_result
        assert "original_data" in first_result

        # 验证映射结果
        if first_result["key_value"] == "A001":
            assert first_result["mapped_value"] == "CLOUD001"
            assert first_result["mapping_found"] is True

    def test_export_unmapped_keys(self, sample_mapping_dict, sample_test_data, temp_dir):
        """测试导出未映射的键值"""
        self.mapper.load_mapping(sample_mapping_dict)

        # 执行映射以生成未映射的键
        self.mapper.map_data(
            sample_test_data,
            key_column="产品代码",
            data_columns=["产品名称[标准产品名]"],
            value_column_config="云序号[云序号]",
            date_period="2025-01-15"
        )

        output_path = temp_dir / "unmapped_keys.xlsx"
        success, error_msg = self.mapper.export_unmapped_keys(str(output_path))

        assert success
        assert error_msg == ""
        assert output_path.exists()

        # 验证导出的数据
        df_read = pd.read_excel(output_path, sheet_name='未映射键值')
        assert len(df_read) == 1
        assert df_read.iloc[0, 0] == "A004"

    def test_export_unmapped_keys_no_unmapped(self, sample_mapping_dict, temp_dir):
        """测试导出未映射键值但没有未映射数据的情况"""
        self.mapper.load_mapping(sample_mapping_dict)

        output_path = temp_dir / "unmapped_keys.xlsx"
        success, error_msg = self.mapper.export_unmapped_keys(str(output_path))

        assert not success
        assert "没有未映射的键值" in error_msg
