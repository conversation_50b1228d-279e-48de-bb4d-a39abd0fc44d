"""
数据验证工具
提供配置格式验证和解析功能
"""

import re
from typing import Tuple, Optional


class ColumnConfigValidator:
    """列配置验证器"""

    # 匹配格式：原列名[新列名] 或 原列名 或 原列名[新列名,运算表达式]
    COLUMN_PATTERN = re.compile(r'^([^[\]]+)(?:\[([^[\],]+)(?:,([+\-*/])([0-9.]+))?\])?$')

    # 支持的运算符
    SUPPORTED_OPERATORS = {'+', '-', '*', '/'}

    @classmethod
    def _validate_operation(cls, operator: str, operand: str) -> Tuple[bool, str]:
        """
        验证运算操作

        Args:
            operator: 运算符 (+, -, *, /)
            operand: 操作数

        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        if operator not in cls.SUPPORTED_OPERATORS:
            return False, f"不支持的运算符: {operator}，支持的运算符: {', '.join(cls.SUPPORTED_OPERATORS)}"

        try:
            float(operand)
            if operator == '/' and float(operand) == 0:
                return False, "除数不能为零"
        except ValueError:
            return False, f"无效的操作数: {operand}，必须是数字"

        return True, ""

    @classmethod
    def validate_column_format(cls, text: str) -> Tuple[bool, str, Optional[str], Optional[str], Optional[str], Optional[str]]:
        """
        验证列配置格式

        Args:
            text: 输入的配置文本

        Returns:
            Tuple[bool, str, Optional[str], Optional[str], Optional[str], Optional[str]]:
            (是否有效, 错误信息, 原列名, 新列名, 运算符, 操作数)
        """
        if not text or not text.strip():
            return False, "配置不能为空", None, None, None, None

        text = text.strip()
        match = cls.COLUMN_PATTERN.match(text)

        if not match:
            return False, "格式错误，请使用：原列名[新列名] 或 原列名 或 原列名[新列名,运算表达式]", None, None, None, None

        original_name = match.group(1).strip()
        new_name = match.group(2).strip() if match.group(2) else None
        operator = match.group(3) if match.group(3) else None
        operand = match.group(4) if match.group(4) else None

        if not original_name:
            return False, "原列名不能为空", None, None, None, None

        if new_name is not None and not new_name:
            return False, "新列名不能为空", None, None, None, None

        # 验证运算表达式
        if operator is not None or operand is not None:
            if operator is None or operand is None:
                return False, "运算表达式不完整，格式：[新列名,运算符操作数]", None, None, None, None

            is_valid, error_msg = cls._validate_operation(operator, operand)
            if not is_valid:
                return False, error_msg, None, None, None, None

        return True, "", original_name, new_name, operator, operand

    @classmethod
    def parse_column_config(cls, text: str) -> Tuple[str, Optional[str], Optional[str], Optional[str]]:
        """
        解析列配置

        Args:
            text: 配置文本

        Returns:
            Tuple[str, Optional[str], Optional[str], Optional[str]]: (原列名, 新列名, 运算符, 操作数)
        """
        is_valid, _, original_name, new_name, operator, operand = cls.validate_column_format(text)
        if is_valid:
            return original_name, new_name, operator, operand
        return "", None, None, None

    @classmethod
    def format_column_config(cls, original_name: str, new_name: Optional[str] = None,
                           operator: Optional[str] = None, operand: Optional[str] = None) -> str:
        """
        格式化列配置

        Args:
            original_name: 原列名
            new_name: 新列名（可选）
            operator: 运算符（可选）
            operand: 操作数（可选）

        Returns:
            str: 格式化后的配置文本
        """
        if new_name:
            if operator and operand:
                return f"{original_name}[{new_name},{operator}{operand}]"
            else:
                return f"{original_name}[{new_name}]"
        return original_name

    @classmethod
    def get_format_examples(cls) -> list:
        """获取格式示例"""
        return [
            "产品名称[标准产品名]",
            "客户代码[客户ID]",
            "序号[云序号]",
            "CPU使用率平均值（%）[CPU使用率,/100]",
            "价格[单价,*1.1]",
            "库存[数量,-10]",
            "基数[总数,+50]",
            "地区代码",
            "产品代码"
        ]

    @classmethod
    def apply_operation(cls, value, operator: str, operand: str):
        """
        对数值应用运算操作

        Args:
            value: 原始值
            operator: 运算符
            operand: 操作数

        Returns:
            运算后的值
        """
        try:
            # 尝试转换为数值
            numeric_value = float(value)
            numeric_operand = float(operand)

            if operator == '+':
                return numeric_value + numeric_operand
            elif operator == '-':
                return numeric_value - numeric_operand
            elif operator == '*':
                return numeric_value * numeric_operand
            elif operator == '/':
                if numeric_operand == 0:
                    return value  # 保持原值，避免除零错误
                return numeric_value / numeric_operand
            else:
                return value  # 不支持的运算符，保持原值
        except (ValueError, TypeError):
            # 如果不能转换为数值，返回原值
            return value

    @classmethod
    def get_format_help(cls) -> str:
        """获取格式帮助文本"""
        return """
列配置格式说明：
1. 基本格式：原列名[新列名]
2. 保持原名：原列名
3. 数值运算：原列名[新列名,运算符操作数]

支持的运算符：
• + 加法：价格[单价,+10]
• - 减法：库存[数量,-5]
• * 乘法：重量[重量KG,*0.001]
• / 除法：百分比[比例,/100]

示例：
• CPU使用率平均值（%）[CPU使用率,/100] - 除以100转换为小数
• 价格[单价,*1.1] - 乘以1.1增加10%
• 库存[数量,-10] - 减去10个安全库存
• 基数[总数,+50] - 加上50个基础值
"""


class WorksheetValidator:
    """工作表验证器"""

    @classmethod
    def validate_worksheet_name(cls, name: str) -> Tuple[bool, str]:
        """
        验证工作表名称

        Args:
            name: 工作表名称

        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        if not name or not name.strip():
            return False, "工作表名称不能为空"

        name = name.strip()

        # Excel工作表名称限制
        invalid_chars = ['\\', '/', '*', '?', ':', '[', ']']
        for char in invalid_chars:
            if char in name:
                return False, f"工作表名称不能包含字符: {char}"

        if len(name) > 31:
            return False, "工作表名称不能超过31个字符"

        return True, ""

    @classmethod
    def get_common_worksheet_names(cls) -> list:
        """获取常见工作表名称"""
        return [
            "Sheet1",
            "工作表1",
            "数据",
            "映射表",
            "主表",
            "原始数据"
        ]
