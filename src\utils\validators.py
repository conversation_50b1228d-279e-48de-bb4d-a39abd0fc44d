"""
数据验证工具
提供配置格式验证和解析功能
"""

import re
from typing import Tuple, Optional


class ColumnConfigValidator:
    """列配置验证器"""
    
    # 匹配格式：原列名[新列名] 或 原列名
    COLUMN_PATTERN = re.compile(r'^([^[\]]+)(?:\[([^[\]]+)\])?$')
    
    @classmethod
    def validate_column_format(cls, text: str) -> Tuple[bool, str, Optional[str], Optional[str]]:
        """
        验证列配置格式
        
        Args:
            text: 输入的配置文本
            
        Returns:
            Tuple[bool, str, Optional[str], Optional[str]]: 
            (是否有效, 错误信息, 原列名, 新列名)
        """
        if not text or not text.strip():
            return False, "配置不能为空", None, None
        
        text = text.strip()
        match = cls.COLUMN_PATTERN.match(text)
        
        if not match:
            return False, "格式错误，请使用：原列名[新列名] 或 原列名", None, None
        
        original_name = match.group(1).strip()
        new_name = match.group(2).strip() if match.group(2) else None
        
        if not original_name:
            return False, "原列名不能为空", None, None
        
        if new_name is not None and not new_name:
            return False, "新列名不能为空", None, None
        
        return True, "", original_name, new_name
    
    @classmethod
    def parse_column_config(cls, text: str) -> Tuple[str, Optional[str]]:
        """
        解析列配置
        
        Args:
            text: 配置文本
            
        Returns:
            Tuple[str, Optional[str]]: (原列名, 新列名)
        """
        is_valid, _, original_name, new_name = cls.validate_column_format(text)
        if is_valid:
            return original_name, new_name
        return "", None
    
    @classmethod
    def format_column_config(cls, original_name: str, new_name: Optional[str] = None) -> str:
        """
        格式化列配置
        
        Args:
            original_name: 原列名
            new_name: 新列名（可选）
            
        Returns:
            str: 格式化后的配置文本
        """
        if new_name:
            return f"{original_name}[{new_name}]"
        return original_name
    
    @classmethod
    def get_format_examples(cls) -> list:
        """获取格式示例"""
        return [
            "产品名称[标准产品名]",
            "客户代码[客户ID]", 
            "序号[云序号]",
            "地区代码",
            "产品代码"
        ]
    
    @classmethod
    def get_format_help(cls) -> str:
        """获取格式帮助文本"""
        return """
        <b>配置格式说明：</b><br>
        • <b>带重命名</b>：原列名[新列名]，如 "产品名称[标准产品名]"<br>
        • <b>不重命名</b>：原列名，如 "产品代码"<br>
        • 方括号内为输出文件中显示的列名<br>
        • 原列名必须与Excel文件中的列名完全匹配
        """


class WorksheetValidator:
    """工作表验证器"""
    
    @classmethod
    def validate_worksheet_name(cls, name: str) -> Tuple[bool, str]:
        """
        验证工作表名称
        
        Args:
            name: 工作表名称
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        if not name or not name.strip():
            return False, "工作表名称不能为空"
        
        name = name.strip()
        
        # Excel工作表名称限制
        invalid_chars = ['\\', '/', '*', '?', ':', '[', ']']
        for char in invalid_chars:
            if char in name:
                return False, f"工作表名称不能包含字符: {char}"
        
        if len(name) > 31:
            return False, "工作表名称不能超过31个字符"
        
        return True, ""
    
    @classmethod
    def get_common_worksheet_names(cls) -> list:
        """获取常见工作表名称"""
        return [
            "Sheet1",
            "工作表1", 
            "数据",
            "映射表",
            "主表",
            "原始数据"
        ]
