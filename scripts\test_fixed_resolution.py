#!/usr/bin/env python3
"""
测试固定分辨率功能
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

def test_display_settings():
    """测试显示设置配置"""
    print("🧪 测试显示设置配置...")
    
    try:
        from config.display_settings import (
            RESOLUTION_PRESETS, 
            get_resolution_config,
            validate_resolution,
            get_optimal_resolution_for_screen,
            get_layout_for_resolution
        )
        
        print("✅ 显示设置模块导入成功")
        
        # 测试分辨率预设
        print("\n📐 可用分辨率预设:")
        for name, config in RESOLUTION_PRESETS.items():
            print(f"  • {config['name']}: {config['width']}x{config['height']} - {config['description']}")
        
        # 测试分辨率验证
        print("\n🔍 测试分辨率验证:")
        test_cases = [
            (1280, 800),
            (800, 600),  # 太小
            (1920, 1080),
            (1024, 640),  # 最小值
        ]
        
        for width, height in test_cases:
            is_valid, adj_width, adj_height = validate_resolution(width, height)
            status = "✅" if is_valid else "⚠️"
            print(f"  {status} {width}x{height} -> {adj_width}x{adj_height} (有效: {is_valid})")
        
        # 测试屏幕适配
        print("\n🖥️ 测试屏幕适配:")
        screen_sizes = [
            (1366, 768),   # 笔记本
            (1920, 1080),  # 标准显示器
            (2560, 1440),  # 高分辨率
            (1024, 768),   # 小屏幕
        ]
        
        for screen_w, screen_h in screen_sizes:
            config = get_optimal_resolution_for_screen(screen_w, screen_h)
            print(f"  屏幕 {screen_w}x{screen_h} -> 推荐 {config['name']} ({config['width']}x{config['height']})")
        
        # 测试布局调整
        print("\n📏 测试布局调整:")
        for width, height in [(1024, 640), (1280, 800), (1440, 900)]:
            layout = get_layout_for_resolution(width, height)
            print(f"  {width}x{height}: 侧边栏宽度={layout['sidebar_width']}, 内容边距={layout['content_margin']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 显示设置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_fixed_resolution_gui():
    """测试固定分辨率GUI"""
    print("\n🧪 测试固定分辨率GUI...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from gui.main_window import MainWindow
        
        # 启用高DPI支持
        if hasattr(Qt, 'AA_EnableHighDpiScaling'):
            QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
            QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("OFE2E 固定分辨率测试")
        app.setApplicationVersion("1.0.0")
        
        # 创建主窗口
        window = MainWindow()
        
        print("✅ 固定分辨率GUI创建成功")
        print(f"✅ 窗口尺寸: {window.fixed_width}x{window.fixed_height}")
        print(f"✅ 分辨率配置: {window.resolution_config['name']}")
        
        # 显示窗口
        window.show()
        
        # 添加测试信息
        window.log_message("🎯 固定分辨率测试启动")
        window.log_message(f"窗口尺寸: {window.fixed_width}x{window.fixed_height}")
        window.log_message(f"分辨率配置: {window.resolution_config['name']}")
        window.log_message(f"描述: {window.resolution_config['description']}")
        window.log_message("")
        window.log_message("✨ 固定分辨率优势:")
        window.log_message("  • 界面布局始终保持最佳状态")
        window.log_message("  • 避免拉伸导致的界面变形")
        window.log_message("  • 确保在不同显示器上的一致体验")
        window.log_message("  • 优化的控件尺寸和间距")
        window.log_message("")
        window.log_message("🔧 测试内容:")
        window.log_message("  • 窗口无法手动调整大小")
        window.log_message("  • 界面元素比例完美")
        window.log_message("  • 文字和图标清晰显示")
        
        print("\n🎉 固定分辨率测试成功!")
        print("请检查以下内容:")
        print("1. 窗口尺寸固定，无法拖拽调整")
        print("2. 界面元素比例协调美观")
        print("3. 文字清晰，控件对齐良好")
        print("4. 整体布局紧凑且实用")
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 OFE2E 固定分辨率测试")
    print("=" * 50)
    
    # 测试显示设置配置
    if not test_display_settings():
        return
    
    print("\n" + "=" * 50)
    
    # 测试固定分辨率GUI
    test_fixed_resolution_gui()


if __name__ == "__main__":
    main()
