#!/usr/bin/env python3
"""
端到端测试
使用docs/box/input下的真实文件进行完整流程测试
"""

import pytest
import pandas as pd
from pathlib import Path
import tempfile
import shutil

from core.excel_handler import ExcelHandler
from core.mapper import DataMapper
from core.data_processor import DataProcessor, ProcessingConfig


class TestEndToEnd:
    """端到端测试类"""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """测试设置"""
        self.input_dir = Path("docs/box/input")
        self.temp_output_dir = Path(tempfile.mkdtemp())
        self.excel_handler = ExcelHandler()
        self.data_mapper = DataMapper()
        self.data_processor = DataProcessor()
        
        yield
        
        # 清理临时目录
        if self.temp_output_dir.exists():
            shutil.rmtree(self.temp_output_dir)
    
    def test_input_files_exist(self):
        """测试输入文件是否存在"""
        assert self.input_dir.exists(), f"输入目录不存在: {self.input_dir}"
        
        # 检查数据源文件
        data_files = list(self.input_dir.glob("数据源文件*.xlsx"))
        assert len(data_files) > 0, "没有找到数据源文件"
        
        # 检查映射文件
        mapping_files = list(self.input_dir.glob("映射文件*.xlsx"))
        assert len(mapping_files) > 0, "没有找到映射文件"
    
    def test_data_source_file_structure(self):
        """测试数据源文件结构"""
        data_files = list(self.input_dir.glob("数据源文件*.xlsx"))
        
        for data_file in data_files:
            # 验证文件结构
            is_valid, error_msg = self.excel_handler.validate_data_source_structure(str(data_file))
            assert is_valid, f"数据源文件结构无效 {data_file.name}: {error_msg}"
            
            # 提取日期
            success, error_msg, date_period = self.excel_handler.extract_date_from_filter_sheet(str(data_file))
            assert success, f"日期提取失败 {data_file.name}: {error_msg}"
            assert date_period is not None, f"日期为空 {data_file.name}"
            
            # 读取表格数据
            df = pd.read_excel(data_file, sheet_name="表格")
            assert len(df) > 0, f"表格数据为空 {data_file.name}"
            assert 'UUID' in df.columns, f"缺少UUID列 {data_file.name}"
    
    def test_mapping_file_structure(self):
        """测试映射文件结构"""
        mapping_files = list(self.input_dir.glob("映射文件*.xlsx"))
        
        for mapping_file in mapping_files:
            # 获取工作表
            sheet_names = self.excel_handler.get_worksheet_names(str(mapping_file))
            assert len(sheet_names) > 0, f"映射文件没有工作表 {mapping_file.name}"
            
            # 检查至少有一个有效的映射工作表
            valid_sheets = 0
            for sheet_name in sheet_names:
                columns = self.excel_handler.get_column_names(str(mapping_file), sheet_name)
                if len(columns) >= 2:
                    valid_sheets += 1
            
            assert valid_sheets > 0, f"映射文件没有有效的映射工作表 {mapping_file.name}"
    
    def test_mapping_data_loading(self):
        """测试映射数据加载"""
        mapping_files = list(self.input_dir.glob("映射文件*.xlsx"))
        mapping_file = str(mapping_files[0])
        
        # 读取映射数据
        success, error_msg, mapping_dict = self.excel_handler.read_mapping_data(
            mapping_file, "CVM - OMP", "接口标识", "云序号"
        )
        
        assert success, f"映射数据读取失败: {error_msg}"
        assert len(mapping_dict) > 0, "映射字典为空"
        
        # 加载到映射器
        self.data_mapper.load_mapping(mapping_dict)
        assert len(self.data_mapper.mapping_dict) > 0, "映射器加载失败"
    
    def test_data_mapping_functionality(self):
        """测试数据映射功能"""
        # 准备测试数据
        data_files = list(self.input_dir.glob("数据源文件*.xlsx"))
        mapping_files = list(self.input_dir.glob("映射文件*.xlsx"))
        
        data_file = str(data_files[0])
        mapping_file = str(mapping_files[0])
        
        # 读取数据
        data_df = pd.read_excel(data_file, sheet_name="表格")
        
        # 读取映射数据
        success, error_msg, mapping_dict = self.excel_handler.read_mapping_data(
            mapping_file, "CVM - OMP", "接口标识", "云序号"
        )
        assert success, f"映射数据读取失败: {error_msg}"
        
        # 加载映射
        self.data_mapper.load_mapping(mapping_dict)
        
        # 执行映射
        success, error_msg, result_df = self.data_mapper.map_data(
            data_df, "UUID", ["UUID[UUID]"], "云序号", "2025-05-19"
        )
        
        assert success, f"数据映射失败: {error_msg}"
        assert result_df is not None, "映射结果为空"
        assert len(result_df) > 0, "映射结果没有数据"
        assert '云序号' in result_df.columns, "映射结果缺少云序号列"
        assert 'UUID' in result_df.columns, "映射结果缺少UUID列"
        assert '周期' in result_df.columns, "映射结果缺少周期列"
    
    def test_processing_configuration_creation(self):
        """测试处理配置创建"""
        data_files = [str(f) for f in self.input_dir.glob("数据源文件*.xlsx")]
        mapping_files = [str(f) for f in self.input_dir.glob("映射文件*.xlsx")]
        
        # 创建配置
        config = ProcessingConfig()
        config.data_files = data_files
        config.mapping_file = mapping_files[0]
        config.mapping_sheet = "CVM - OMP"
        config.mapping_key_column = "接口标识"
        config.mapping_value_column = "云序号[云序号]"
        config.data_column = "UUID[UUID]"
        config.data_key_column = "UUID"
        config.output_directory = str(self.temp_output_dir)
        
        # 验证配置
        is_valid, errors = config.validate()
        assert is_valid, f"配置验证失败: {errors}"
    
    def test_full_processing_pipeline(self):
        """测试完整处理流程"""
        data_files = [str(f) for f in self.input_dir.glob("数据源文件*.xlsx")]
        mapping_files = [str(f) for f in self.input_dir.glob("映射文件*.xlsx")]
        
        # 创建配置
        config = ProcessingConfig()
        config.data_files = data_files
        config.mapping_file = mapping_files[0]
        config.mapping_sheet = "CVM - OMP"
        config.mapping_key_column = "接口标识"
        config.mapping_value_column = "云序号[云序号]"
        config.data_column = "UUID[UUID]"
        config.data_key_column = "UUID"
        config.output_directory = str(self.temp_output_dir)
        
        # 进度和日志回调
        progress_messages = []
        log_messages = []
        
        def progress_callback(progress, message):
            progress_messages.append((progress, message))
        
        def log_callback(message):
            log_messages.append(message)
        
        # 执行处理
        result = self.data_processor.process_data(
            config,
            progress_callback=progress_callback,
            log_callback=log_callback
        )
        
        # 验证结果
        assert result is not None, "处理结果为空"
        assert result.success, f"处理失败: {result.errors}"
        assert result.processed_files > 0, "没有处理任何文件"
        assert len(result.output_files) > 0, "没有生成输出文件"
        
        # 验证输出文件
        for output_file in result.output_files:
            assert Path(output_file).exists(), f"输出文件不存在: {output_file}"
            
            # 验证文件内容
            df = pd.read_excel(output_file, sheet_name='处理结果')
            assert len(df) > 0, f"输出文件为空: {output_file}"
            assert '云序号' in df.columns, f"输出文件缺少云序号列: {output_file}"
            assert 'UUID' in df.columns, f"输出文件缺少UUID列: {output_file}"
            assert '周期' in df.columns, f"输出文件缺少周期列: {output_file}"
        
        # 验证回调被调用
        assert len(progress_messages) > 0, "进度回调未被调用"
        assert len(log_messages) > 0, "日志回调未被调用"
    
    def test_mapping_coverage_validation(self):
        """测试映射覆盖率验证"""
        data_files = list(self.input_dir.glob("数据源文件*.xlsx"))
        mapping_files = list(self.input_dir.glob("映射文件*.xlsx"))
        
        data_file = str(data_files[0])
        mapping_file = str(mapping_files[0])
        
        # 读取数据
        data_df = pd.read_excel(data_file, sheet_name="表格")
        
        # 读取映射数据
        success, error_msg, mapping_dict = self.excel_handler.read_mapping_data(
            mapping_file, "CVM - OMP", "接口标识", "云序号"
        )
        assert success, f"映射数据读取失败: {error_msg}"
        
        # 加载映射
        self.data_mapper.load_mapping(mapping_dict)
        
        # 验证覆盖率
        coverage_rate, uncovered_keys = self.data_mapper.validate_mapping_coverage(data_df, "UUID")
        
        assert coverage_rate >= 0, "覆盖率应该大于等于0"
        assert coverage_rate <= 100, "覆盖率应该小于等于100"
        assert isinstance(uncovered_keys, list), "未覆盖键应该是列表"
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试无效配置
        config = ProcessingConfig()
        # 不设置任何配置项
        
        result = self.data_processor.process_data(config)
        assert result is not None, "处理结果不应为空"
        assert not result.success, "无效配置应该导致处理失败"
        assert len(result.errors) > 0, "应该有错误信息"
    
    @pytest.mark.parametrize("data_file_pattern", ["数据源文件*.xlsx"])
    def test_individual_data_files(self, data_file_pattern):
        """测试单个数据文件处理"""
        data_files = list(self.input_dir.glob(data_file_pattern))
        
        for data_file in data_files:
            # 验证文件可以正常读取
            df = pd.read_excel(data_file, sheet_name="表格")
            assert len(df) > 0, f"数据文件为空: {data_file.name}"
            
            # 验证必要的列存在
            assert 'UUID' in df.columns, f"缺少UUID列: {data_file.name}"
            
            # 验证数据类型
            uuid_series = df['UUID']
            assert not uuid_series.empty, f"UUID列为空: {data_file.name}"
