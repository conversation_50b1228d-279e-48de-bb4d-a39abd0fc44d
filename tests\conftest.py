"""
pytest配置文件
提供测试夹具和配置
"""

import pytest
import pandas as pd
import tempfile
import shutil
from pathlib import Path
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


@pytest.fixture
def temp_dir():
    """创建临时目录夹具"""
    temp_path = tempfile.mkdtemp()
    yield Path(temp_path)
    shutil.rmtree(temp_path)


@pytest.fixture
def sample_data_source_file(temp_dir):
    """创建示例数据源文件"""
    # 创建过滤条件数据
    filter_data = pd.DataFrame({
        "条件": ["时间范围"],
        "值": ["(UTC+08:00)2025-01-15 08:00:00 ~ 2025-01-15 20:00:00"]
    })
    
    # 创建表格数据
    table_data = pd.DataFrame({
        "产品代码": ["A001", "A002", "A003", "A004"],
        "产品名称": ["产品A", "产品B", "产品C", "产品D"],
        "销量": [100, 200, 300, 150],
        "价格": [10.5, 20.0, 15.8, 12.3]
    })
    
    # 写入Excel文件
    file_path = temp_dir / "test_data_source.xlsx"
    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
        filter_data.to_excel(writer, sheet_name='过滤条件', index=False)
        table_data.to_excel(writer, sheet_name='表格', index=False)
    
    return str(file_path)


@pytest.fixture
def sample_mapping_file(temp_dir):
    """创建示例映射文件"""
    mapping_data = pd.DataFrame({
        "接口标识": ["A001", "A002", "A003"],
        "云序号": ["CLOUD001", "CLOUD002", "CLOUD003"],
        "描述": ["云产品A", "云产品B", "云产品C"]
    })
    
    file_path = temp_dir / "test_mapping.xlsx"
    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
        mapping_data.to_excel(writer, sheet_name='Sheet1', index=False)
        mapping_data.to_excel(writer, sheet_name='映射表', index=False)
    
    return str(file_path)


@pytest.fixture
def invalid_data_source_file(temp_dir):
    """创建无效的数据源文件（缺少必要工作表）"""
    data = pd.DataFrame({
        "列1": ["值1", "值2"],
        "列2": ["值3", "值4"]
    })
    
    file_path = temp_dir / "invalid_data_source.xlsx"
    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
        data.to_excel(writer, sheet_name='错误工作表', index=False)
    
    return str(file_path)


@pytest.fixture
def sample_test_data():
    """创建示例测试数据"""
    return pd.DataFrame({
        "产品代码": ["A001", "A002", "A004", "A003"],
        "产品名称": ["产品A", "产品B", "产品D", "产品C"],
        "销量": [100, 200, 150, 300]
    })


@pytest.fixture
def sample_mapping_dict():
    """创建示例映射字典"""
    return {
        "A001": "CLOUD001",
        "A002": "CLOUD002", 
        "A003": "CLOUD003"
    }


@pytest.fixture
def processing_config_data():
    """创建处理配置数据"""
    return {
        "data_files": ["test1.xlsx", "test2.xlsx"],
        "mapping_file": "mapping.xlsx",
        "mapping_sheet": "Sheet1",
        "mapping_key_column": "接口标识",
        "mapping_value_column": "云序号[云序号]",
        "data_column": "产品名称[标准产品名]",
        "data_key_column": "产品代码",
        "output_directory": "output"
    }
