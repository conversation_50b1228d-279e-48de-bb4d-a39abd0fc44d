#!/usr/bin/env python3
"""
UV环境设置脚本
用于初始化UV环境和安装依赖
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(cmd, check=True):
    """运行命令并处理错误"""
    print(f"执行命令: {' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, check=check, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        if check:
            sys.exit(1)
        return e


def check_uv_installed():
    """检查UV是否已安装"""
    try:
        result = subprocess.run(["uv", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"UV已安装: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass
    return False


def install_uv():
    """安装UV"""
    print("正在安装UV...")
    if sys.platform == "win32":
        # Windows安装方式
        cmd = ["pip", "install", "uv"]
    else:
        # Unix/Linux安装方式
        cmd = ["curl", "-LsSf", "https://astral.sh/uv/install.sh", "|", "sh"]
    
    run_command(cmd)


def setup_project():
    """设置项目环境"""
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    print("初始化UV项目...")
    
    # 创建虚拟环境
    run_command(["uv", "venv", "--python", "3.11"])
    
    # 同步依赖
    run_command(["uv", "sync"])
    
    # 安装开发依赖
    run_command(["uv", "sync", "--extra", "dev"])
    
    # 安装构建依赖
    run_command(["uv", "sync", "--extra", "build"])
    
    print("\n✅ UV环境设置完成!")
    print("使用以下命令激活环境:")
    print("  Windows: .venv\\Scripts\\activate")
    print("  Unix/Linux: source .venv/bin/activate")
    print("\n或者使用UV运行命令:")
    print("  uv run python main.py")
    print("  uv run python test_gui.py")


def main():
    """主函数"""
    print("🚀 开始设置OFE2E项目UV环境...")
    
    # 检查并安装UV
    if not check_uv_installed():
        install_uv()
    
    # 设置项目
    setup_project()


if __name__ == "__main__":
    main()
