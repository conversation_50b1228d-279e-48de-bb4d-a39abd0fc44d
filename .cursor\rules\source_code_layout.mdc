---
description: 
globs: 
alwaysApply: false
---
# Source Code Layout

The primary location for the project's source code is the `[src/](mdc:src)` directory.
Within `[src/](mdc:src)`, the code is further organized into subdirectories:
- `[src/config/](mdc:src/config)`: Likely contains configuration-specific source code.
- `[src/core/](mdc:src/core)`: Probably holds the core logic of the application.
- `[src/gui/](mdc:src/gui)`: Contains the graphical user interface code.
- `[src/utils/](mdc:src/utils)`: Likely for utility functions and helper modules.

