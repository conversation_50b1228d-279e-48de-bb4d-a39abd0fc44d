#!/usr/bin/env python3
"""
多列配置功能的pytest测试
测试ProcessingConfig类的多列数据配置功能
"""

import pytest
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from core.data_processor import ProcessingConfig
from utils.validators import ColumnConfigValidator


class TestMultiColumnConfiguration:
    """测试多列配置功能"""

    def test_single_column_backward_compatibility(self):
        """测试单列配置的向后兼容性"""
        config = ProcessingConfig()
        
        # 设置单列配置
        config.data_column = "UUID[云序号]"
        
        # 验证向后兼容性
        assert config.data_column == "UUID[云序号]"
        assert config.data_columns == ["UUID[云序号]"]
        assert config.get_data_columns_string() == "UUID[云序号]"

    def test_single_column_without_rename(self):
        """测试单列配置（不重命名）"""
        config = ProcessingConfig()
        
        config.data_column = "UUID"
        
        assert config.data_column == "UUID"
        assert config.data_columns == ["UUID"]
        assert config.get_data_columns_string() == "UUID"

    def test_multi_column_configuration(self):
        """测试多列配置功能"""
        config = ProcessingConfig()
        
        # 设置多列配置
        multi_column_config = "UUID[云序号];名称[产品名称];规格[产品规格];状态"
        config.set_data_columns_from_string(multi_column_config)
        
        # 验证多列配置
        expected_columns = ["UUID[云序号]", "名称[产品名称]", "规格[产品规格]", "状态"]
        assert config.data_columns == expected_columns
        assert config.data_column == "UUID[云序号]"  # 第一列
        assert config.get_data_columns_string() == multi_column_config

    def test_multi_column_mixed_formats(self):
        """测试多列配置混合格式"""
        config = ProcessingConfig()
        
        # 混合重命名和保持原名
        config.set_data_columns_from_string("产品代码[标准代码];产品名称[标准名称];价格[单价];库存")
        
        expected = ["产品代码[标准代码]", "产品名称[标准名称]", "价格[单价]", "库存"]
        assert config.data_columns == expected

    def test_empty_column_configuration(self):
        """测试空列配置"""
        config = ProcessingConfig()
        
        config.set_data_columns_from_string("")
        assert config.data_columns == []
        assert config.data_column == ""

    def test_whitespace_handling(self):
        """测试空白字符处理"""
        config = ProcessingConfig()
        
        # 包含空格的配置
        config.set_data_columns_from_string("  UUID[云序号]  ;  名称[产品名称]  ;  规格  ")
        
        expected = ["UUID[云序号]", "名称[产品名称]", "规格"]
        assert config.data_columns == expected

    def test_data_columns_property_setter(self):
        """测试data_columns属性设置器"""
        config = ProcessingConfig()
        
        # 直接设置data_columns列表
        columns = ["UUID[云序号]", "名称[产品名称]", "规格"]
        config.data_columns = columns
        
        assert config.data_columns == columns
        assert config.data_column == "UUID[云序号]"

    def test_configuration_validation_single_column(self):
        """测试单列配置验证"""
        config = ProcessingConfig()
        
        # 设置完整的有效配置
        config.data_files = ["test.xlsx"]
        config.mapping_file = "mapping.xlsx"
        config.mapping_sheet = "Sheet1"
        config.mapping_key_column = "接口标识"
        config.mapping_value_column = "云序号[云序号]"
        config.data_column = "UUID[云序号]"
        config.data_key_column = "UUID"
        config.output_directory = "output"
        
        is_valid, errors = config.validate()
        assert is_valid
        assert len(errors) == 0

    def test_configuration_validation_multi_column(self):
        """测试多列配置验证"""
        config = ProcessingConfig()
        
        # 设置完整的有效多列配置
        config.data_files = ["test.xlsx"]
        config.mapping_file = "mapping.xlsx"
        config.mapping_sheet = "Sheet1"
        config.mapping_key_column = "接口标识"
        config.mapping_value_column = "云序号[云序号]"
        config.set_data_columns_from_string("UUID[云序号];名称[产品名称];规格")
        config.data_key_column = "UUID"
        config.output_directory = "output"
        
        is_valid, errors = config.validate()
        assert is_valid
        assert len(errors) == 0

    def test_configuration_validation_invalid_column_format(self):
        """测试无效列格式验证"""
        config = ProcessingConfig()
        
        # 设置基本配置
        config.data_files = ["test.xlsx"]
        config.mapping_file = "mapping.xlsx"
        config.mapping_sheet = "Sheet1"
        config.mapping_key_column = "接口标识"
        config.mapping_value_column = "云序号[云序号]"
        config.data_key_column = "UUID"
        config.output_directory = "output"
        
        # 设置无效的多列配置
        config.set_data_columns_from_string("UUID[云序号];名称[;规格")
        
        is_valid, errors = config.validate()
        assert not is_valid
        assert any("数据列" in error and "配置格式错误" in error for error in errors)

    def test_configuration_validation_missing_data_columns(self):
        """测试缺少数据列配置的验证"""
        config = ProcessingConfig()
        
        # 设置基本配置但不设置数据列
        config.data_files = ["test.xlsx"]
        config.mapping_file = "mapping.xlsx"
        config.mapping_sheet = "Sheet1"
        config.mapping_key_column = "接口标识"
        config.mapping_value_column = "云序号[云序号]"
        config.data_key_column = "UUID"
        config.output_directory = "output"
        
        is_valid, errors = config.validate()
        assert not is_valid
        assert "未配置数据列" in errors


class TestColumnFormatValidation:
    """测试列格式验证功能"""

    @pytest.mark.parametrize("input_text,expected_valid,expected_original,expected_new", [
        # 有效格式
        ("UUID[云序号]", True, "UUID", "云序号"),
        ("UUID", True, "UUID", None),
        ("产品名称[标准产品名]", True, "产品名称", "标准产品名"),
        ("Product_Code[产品代码]", True, "Product_Code", "产品代码"),
        ("序号[ID]", True, "序号", "ID"),
        ("客户代码", True, "客户代码", None),
        
        # 无效格式
        ("", False, None, None),
        ("UUID[", False, None, None),
        ("UUID[]", False, None, None),
        ("[新名称]", False, None, None),
        ("UUID[新名称[子名称]]", False, None, None),
        ("UUID[新名称]额外文本", False, None, None),
    ])
    def test_column_format_validation(self, input_text, expected_valid, expected_original, expected_new):
        """测试列格式验证的各种情况"""
        is_valid, error_msg, original_name, new_name = ColumnConfigValidator.validate_column_format(input_text)
        
        assert is_valid == expected_valid
        if expected_valid:
            assert original_name == expected_original
            assert new_name == expected_new
            assert error_msg == ""
        else:
            assert error_msg != ""

    def test_column_format_examples(self):
        """测试格式示例的有效性"""
        examples = ColumnConfigValidator.get_format_examples()
        
        for example in examples:
            is_valid, error_msg, original_name, new_name = ColumnConfigValidator.validate_column_format(example)
            assert is_valid, f"示例 '{example}' 应该是有效的，但验证失败: {error_msg}"
            assert original_name is not None
            assert error_msg == ""

    def test_parse_column_config(self):
        """测试列配置解析"""
        # 有效配置
        original, new = ColumnConfigValidator.parse_column_config("UUID[云序号]")
        assert original == "UUID"
        assert new == "云序号"
        
        original, new = ColumnConfigValidator.parse_column_config("UUID")
        assert original == "UUID"
        assert new is None
        
        # 无效配置
        original, new = ColumnConfigValidator.parse_column_config("UUID[")
        assert original == ""
        assert new is None

    def test_format_column_config(self):
        """测试列配置格式化"""
        # 带重命名
        formatted = ColumnConfigValidator.format_column_config("UUID", "云序号")
        assert formatted == "UUID[云序号]"
        
        # 不重命名
        formatted = ColumnConfigValidator.format_column_config("UUID")
        assert formatted == "UUID"
        
        formatted = ColumnConfigValidator.format_column_config("UUID", None)
        assert formatted == "UUID"


class TestMultiColumnStringParsing:
    """测试多列字符串解析功能"""

    def test_single_column_string(self):
        """测试单列字符串解析"""
        config = ProcessingConfig()
        config.set_data_columns_from_string("UUID[云序号]")
        
        assert config.data_columns == ["UUID[云序号]"]
        assert config.get_data_columns_string() == "UUID[云序号]"

    def test_multi_column_string(self):
        """测试多列字符串解析"""
        config = ProcessingConfig()
        config.set_data_columns_from_string("UUID[云序号];名称[产品名称];规格;状态")
        
        expected = ["UUID[云序号]", "名称[产品名称]", "规格", "状态"]
        assert config.data_columns == expected
        assert config.get_data_columns_string() == "UUID[云序号];名称[产品名称];规格;状态"

    def test_empty_string_parsing(self):
        """测试空字符串解析"""
        config = ProcessingConfig()
        config.set_data_columns_from_string("")
        
        assert config.data_columns == []
        assert config.get_data_columns_string() == ""

    def test_whitespace_only_string(self):
        """测试仅包含空白字符的字符串"""
        config = ProcessingConfig()
        config.set_data_columns_from_string("   ")
        
        assert config.data_columns == []

    def test_string_with_empty_segments(self):
        """测试包含空段的字符串"""
        config = ProcessingConfig()
        config.set_data_columns_from_string("UUID[云序号];;名称[产品名称];")
        
        expected = ["UUID[云序号]", "名称[产品名称]"]
        assert config.data_columns == expected

    def test_complex_multi_column_example(self):
        """测试复杂的多列配置示例"""
        config = ProcessingConfig()
        
        # 复杂的多列配置
        complex_config = "产品代码[标准代码];产品名称[标准名称];价格[单价];库存[数量];状态;创建时间[创建日期]"
        config.set_data_columns_from_string(complex_config)
        
        expected = [
            "产品代码[标准代码]",
            "产品名称[标准名称]", 
            "价格[单价]",
            "库存[数量]",
            "状态",
            "创建时间[创建日期]"
        ]
        assert config.data_columns == expected
        assert len(config.data_columns) == 6
        assert config.data_column == "产品代码[标准代码]"  # 第一列


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
