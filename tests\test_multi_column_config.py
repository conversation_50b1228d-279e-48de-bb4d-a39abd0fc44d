#!/usr/bin/env python3
"""
多列配置功能的pytest测试
测试ProcessingConfig类的多列数据配置功能
"""

import pytest
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from core.data_processor import ProcessingConfig
from utils.validators import ColumnConfigValidator


class TestMultiColumnConfiguration:
    """测试多列配置功能"""

    def test_single_column_backward_compatibility(self):
        """测试单列配置的向后兼容性"""
        config = ProcessingConfig()

        # 设置单列配置
        config.data_column = "UUID[云序号]"

        # 验证向后兼容性
        assert config.data_column == "UUID[云序号]"
        assert config.data_columns == ["UUID[云序号]"]
        assert config.get_data_columns_string() == "UUID[云序号]"

    def test_single_column_without_rename(self):
        """测试单列配置（不重命名）"""
        config = ProcessingConfig()

        config.data_column = "UUID"

        assert config.data_column == "UUID"
        assert config.data_columns == ["UUID"]
        assert config.get_data_columns_string() == "UUID"

    def test_multi_column_configuration(self):
        """测试多列配置功能"""
        config = ProcessingConfig()

        # 设置多列配置
        multi_column_config = "UUID[云序号];名称[产品名称];规格[产品规格];状态"
        config.set_data_columns_from_string(multi_column_config)

        # 验证多列配置
        expected_columns = ["UUID[云序号]", "名称[产品名称]", "规格[产品规格]", "状态"]
        assert config.data_columns == expected_columns
        assert config.data_column == "UUID[云序号]"  # 第一列
        assert config.get_data_columns_string() == multi_column_config

    def test_multi_column_mixed_formats(self):
        """测试多列配置混合格式"""
        config = ProcessingConfig()

        # 混合重命名和保持原名
        config.set_data_columns_from_string("产品代码[标准代码];产品名称[标准名称];价格[单价];库存")

        expected = ["产品代码[标准代码]", "产品名称[标准名称]", "价格[单价]", "库存"]
        assert config.data_columns == expected

    def test_empty_column_configuration(self):
        """测试空列配置"""
        config = ProcessingConfig()

        config.set_data_columns_from_string("")
        assert config.data_columns == []
        assert config.data_column == ""

    def test_whitespace_handling(self):
        """测试空白字符处理"""
        config = ProcessingConfig()

        # 包含空格的配置
        config.set_data_columns_from_string("  UUID[云序号]  ;  名称[产品名称]  ;  规格  ")

        expected = ["UUID[云序号]", "名称[产品名称]", "规格"]
        assert config.data_columns == expected

    def test_data_columns_property_setter(self):
        """测试data_columns属性设置器"""
        config = ProcessingConfig()

        # 直接设置data_columns列表
        columns = ["UUID[云序号]", "名称[产品名称]", "规格"]
        config.data_columns = columns

        assert config.data_columns == columns
        assert config.data_column == "UUID[云序号]"

    def test_configuration_validation_single_column(self):
        """测试单列配置验证"""
        config = ProcessingConfig()

        # 设置完整的有效配置
        config.data_files = ["test.xlsx"]
        config.mapping_file = "mapping.xlsx"
        config.mapping_sheet = "Sheet1"
        config.mapping_key_column = "接口标识"
        config.mapping_value_column = "云序号[云序号]"
        config.data_column = "UUID[云序号]"
        config.data_key_column = "UUID"
        config.output_directory = "output"

        is_valid, errors = config.validate()
        assert is_valid
        assert len(errors) == 0

    def test_configuration_validation_multi_column(self):
        """测试多列配置验证"""
        config = ProcessingConfig()

        # 设置完整的有效多列配置
        config.data_files = ["test.xlsx"]
        config.mapping_file = "mapping.xlsx"
        config.mapping_sheet = "Sheet1"
        config.mapping_key_column = "接口标识"
        config.mapping_value_column = "云序号[云序号]"
        config.set_data_columns_from_string("UUID[云序号];名称[产品名称];规格")
        config.data_key_column = "UUID"
        config.output_directory = "output"

        is_valid, errors = config.validate()
        assert is_valid
        assert len(errors) == 0

    def test_configuration_validation_invalid_column_format(self):
        """测试无效列格式验证"""
        config = ProcessingConfig()

        # 设置基本配置
        config.data_files = ["test.xlsx"]
        config.mapping_file = "mapping.xlsx"
        config.mapping_sheet = "Sheet1"
        config.mapping_key_column = "接口标识"
        config.mapping_value_column = "云序号[云序号]"
        config.data_key_column = "UUID"
        config.output_directory = "output"

        # 设置无效的多列配置
        config.set_data_columns_from_string("UUID[云序号];名称[;规格")

        is_valid, errors = config.validate()
        assert not is_valid
        assert any("数据列" in error and "配置格式错误" in error for error in errors)

    def test_configuration_validation_missing_data_columns(self):
        """测试缺少数据列配置的验证"""
        config = ProcessingConfig()

        # 设置基本配置但不设置数据列
        config.data_files = ["test.xlsx"]
        config.mapping_file = "mapping.xlsx"
        config.mapping_sheet = "Sheet1"
        config.mapping_key_column = "接口标识"
        config.mapping_value_column = "云序号[云序号]"
        config.data_key_column = "UUID"
        config.output_directory = "output"

        is_valid, errors = config.validate()
        assert not is_valid
        assert "未配置数据列" in errors


class TestColumnFormatValidation:
    """测试列格式验证功能"""

    @pytest.mark.parametrize("input_text,expected_valid,expected_original,expected_new,expected_operator,expected_operand", [
        # 有效格式 - 基本
        ("UUID[云序号]", True, "UUID", "云序号", None, None),
        ("UUID", True, "UUID", None, None, None),
        ("产品名称[标准产品名]", True, "产品名称", "标准产品名", None, None),
        ("Product_Code[产品代码]", True, "Product_Code", "产品代码", None, None),
        ("序号[ID]", True, "序号", "ID", None, None),
        ("客户代码", True, "客户代码", None, None, None),

        # 有效格式 - 数值运算
        ("CPU使用率平均值（%）[CPU使用率,/100]", True, "CPU使用率平均值（%）", "CPU使用率", "/", "100"),
        ("价格[单价,*1.1]", True, "价格", "单价", "*", "1.1"),
        ("库存[数量,-10]", True, "库存", "数量", "-", "10"),
        ("基数[总数,+50]", True, "基数", "总数", "+", "50"),
        ("重量[重量KG,*0.001]", True, "重量", "重量KG", "*", "0.001"),
        ("百分比[比例,/100]", True, "百分比", "比例", "/", "100"),

        # 无效格式
        ("", False, None, None, None, None),
        ("UUID[", False, None, None, None, None),
        ("UUID[]", False, None, None, None, None),
        ("[新名称]", False, None, None, None, None),
        ("UUID[新名称[子名称]]", False, None, None, None, None),
        ("UUID[新名称]额外文本", False, None, None, None, None),
        ("价格[单价,/0]", False, None, None, None, None),  # 除零错误
        ("价格[单价,*abc]", False, None, None, None, None),  # 无效操作数
        ("价格[单价,%10]", False, None, None, None, None),  # 不支持的运算符
        ("价格[单价,*]", False, None, None, None, None),  # 缺少操作数
        ("价格[单价,10]", False, None, None, None, None),  # 缺少运算符
    ])
    def test_column_format_validation(self, input_text, expected_valid, expected_original, expected_new, expected_operator, expected_operand):
        """测试列格式验证的各种情况"""
        is_valid, error_msg, original_name, new_name, operator, operand = ColumnConfigValidator.validate_column_format(input_text)

        assert is_valid == expected_valid
        if expected_valid:
            assert original_name == expected_original
            assert new_name == expected_new
            assert operator == expected_operator
            assert operand == expected_operand
            assert error_msg == ""
        else:
            assert error_msg != ""

    def test_column_format_examples(self):
        """测试格式示例的有效性"""
        examples = ColumnConfigValidator.get_format_examples()

        for example in examples:
            is_valid, error_msg, original_name, new_name, operator, operand = ColumnConfigValidator.validate_column_format(example)
            assert is_valid, f"示例 '{example}' 应该是有效的，但验证失败: {error_msg}"
            assert original_name is not None
            assert error_msg == ""

    def test_parse_column_config(self):
        """测试列配置解析"""
        # 有效配置 - 基本
        original, new, operator, operand = ColumnConfigValidator.parse_column_config("UUID[云序号]")
        assert original == "UUID"
        assert new == "云序号"
        assert operator is None
        assert operand is None

        original, new, operator, operand = ColumnConfigValidator.parse_column_config("UUID")
        assert original == "UUID"
        assert new is None
        assert operator is None
        assert operand is None

        # 有效配置 - 数值运算
        original, new, operator, operand = ColumnConfigValidator.parse_column_config("CPU使用率平均值（%）[CPU使用率,/100]")
        assert original == "CPU使用率平均值（%）"
        assert new == "CPU使用率"
        assert operator == "/"
        assert operand == "100"

        # 无效配置
        original, new, operator, operand = ColumnConfigValidator.parse_column_config("UUID[")
        assert original == ""
        assert new is None
        assert operator is None
        assert operand is None

    def test_format_column_config(self):
        """测试列配置格式化"""
        # 带重命名
        formatted = ColumnConfigValidator.format_column_config("UUID", "云序号")
        assert formatted == "UUID[云序号]"

        # 带重命名和数值运算
        formatted = ColumnConfigValidator.format_column_config("CPU使用率平均值（%）", "CPU使用率", "/", "100")
        assert formatted == "CPU使用率平均值（%）[CPU使用率,/100]"

        formatted = ColumnConfigValidator.format_column_config("价格", "单价", "*", "1.1")
        assert formatted == "价格[单价,*1.1]"

        # 不重命名
        formatted = ColumnConfigValidator.format_column_config("UUID")
        assert formatted == "UUID"

        formatted = ColumnConfigValidator.format_column_config("UUID", None)
        assert formatted == "UUID"


class TestMultiColumnStringParsing:
    """测试多列字符串解析功能"""

    def test_single_column_string(self):
        """测试单列字符串解析"""
        config = ProcessingConfig()
        config.set_data_columns_from_string("UUID[云序号]")

        assert config.data_columns == ["UUID[云序号]"]
        assert config.get_data_columns_string() == "UUID[云序号]"

    def test_multi_column_string(self):
        """测试多列字符串解析"""
        config = ProcessingConfig()
        config.set_data_columns_from_string("UUID[云序号];名称[产品名称];规格;状态")

        expected = ["UUID[云序号]", "名称[产品名称]", "规格", "状态"]
        assert config.data_columns == expected
        assert config.get_data_columns_string() == "UUID[云序号];名称[产品名称];规格;状态"

    def test_empty_string_parsing(self):
        """测试空字符串解析"""
        config = ProcessingConfig()
        config.set_data_columns_from_string("")

        assert config.data_columns == []
        assert config.get_data_columns_string() == ""

    def test_whitespace_only_string(self):
        """测试仅包含空白字符的字符串"""
        config = ProcessingConfig()
        config.set_data_columns_from_string("   ")

        assert config.data_columns == []

    def test_string_with_empty_segments(self):
        """测试包含空段的字符串"""
        config = ProcessingConfig()
        config.set_data_columns_from_string("UUID[云序号];;名称[产品名称];")

        expected = ["UUID[云序号]", "名称[产品名称]"]
        assert config.data_columns == expected

    def test_complex_multi_column_example(self):
        """测试复杂的多列配置示例"""
        config = ProcessingConfig()

        # 复杂的多列配置
        complex_config = "产品代码[标准代码];产品名称[标准名称];价格[单价];库存[数量];状态;创建时间[创建日期]"
        config.set_data_columns_from_string(complex_config)

        expected = [
            "产品代码[标准代码]",
            "产品名称[标准名称]",
            "价格[单价]",
            "库存[数量]",
            "状态",
            "创建时间[创建日期]"
        ]
        assert config.data_columns == expected
        assert len(config.data_columns) == 6
        assert config.data_column == "产品代码[标准代码]"  # 第一列


class TestNumericalOperations:
    """测试数值运算功能"""

    @pytest.mark.parametrize("value,operator,operand,expected", [
        # 加法测试
        (100, "+", "50", 150),
        (10.5, "+", "2.5", 13.0),
        ("100", "+", "50", 150),  # 字符串数值

        # 减法测试
        (100, "-", "30", 70),
        (10.5, "-", "2.5", 8.0),
        ("100", "-", "30", 70),

        # 乘法测试
        (100, "*", "1.1", 110.0),
        (50, "*", "2", 100),
        ("100", "*", "1.1", 110.0),

        # 除法测试
        (100, "/", "100", 1.0),
        (50, "/", "2", 25.0),
        ("100", "/", "100", 1.0),

        # 边界情况
        (0, "+", "10", 10),
        (0, "*", "5", 0),
        (100, "/", "1", 100.0),
    ])
    def test_apply_operation_valid(self, value, operator, operand, expected):
        """测试有效的数值运算"""
        result = ColumnConfigValidator.apply_operation(value, operator, operand)
        # 对于浮点数，使用近似比较
        if isinstance(expected, float):
            assert abs(result - expected) < 1e-10
        else:
            assert result == expected

    def test_apply_operation_division_by_zero(self):
        """测试除零情况"""
        # 除零应该返回原值
        result = ColumnConfigValidator.apply_operation(100, "/", "0")
        assert result == 100

    def test_apply_operation_invalid_value(self):
        """测试无效数值"""
        # 非数值应该返回原值
        result = ColumnConfigValidator.apply_operation("abc", "+", "10")
        assert result == "abc"

        result = ColumnConfigValidator.apply_operation(None, "*", "2")
        assert result is None

    def test_apply_operation_invalid_operand(self):
        """测试无效操作数"""
        # 无效操作数应该返回原值
        result = ColumnConfigValidator.apply_operation(100, "+", "abc")
        assert result == 100

    def test_apply_operation_unsupported_operator(self):
        """测试不支持的运算符"""
        # 不支持的运算符应该返回原值
        result = ColumnConfigValidator.apply_operation(100, "%", "10")
        assert result == 100

    def test_multi_column_with_operations(self):
        """测试包含数值运算的多列配置"""
        config = ProcessingConfig()

        # 包含数值运算的多列配置
        multi_config = "状态[状态1];CPU使用率平均值（%）[CPU使用率,/100];价格[单价,*1.1];库存[数量,-10]"
        config.set_data_columns_from_string(multi_config)

        expected = [
            "状态[状态1]",
            "CPU使用率平均值（%）[CPU使用率,/100]",
            "价格[单价,*1.1]",
            "库存[数量,-10]"
        ]
        assert config.data_columns == expected

    def test_operation_validation(self):
        """测试运算操作验证"""
        # 有效运算
        is_valid, error = ColumnConfigValidator._validate_operation("+", "10")
        assert is_valid
        assert error == ""

        is_valid, error = ColumnConfigValidator._validate_operation("/", "100")
        assert is_valid
        assert error == ""

        # 除零错误
        is_valid, error = ColumnConfigValidator._validate_operation("/", "0")
        assert not is_valid
        assert "除数不能为零" in error

        # 无效运算符
        is_valid, error = ColumnConfigValidator._validate_operation("%", "10")
        assert not is_valid
        assert "不支持的运算符" in error

        # 无效操作数
        is_valid, error = ColumnConfigValidator._validate_operation("+", "abc")
        assert not is_valid
        assert "无效的操作数" in error

    def test_format_help_includes_operations(self):
        """测试格式帮助文本包含运算说明"""
        help_text = ColumnConfigValidator.get_format_help()

        # 检查是否包含运算符说明
        assert "+" in help_text
        assert "-" in help_text
        assert "*" in help_text
        assert "/" in help_text
        assert "CPU使用率平均值（%）[CPU使用率,/100]" in help_text


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
