print("开始测试...")

try:
    import sys
    from pathlib import Path
    print("基本导入成功")
    
    # 添加src目录
    sys.path.insert(0, str(Path(__file__).parent / "src"))
    print("路径添加成功")
    
    # 测试pandas
    import pandas as pd
    print("pandas导入成功")
    
    # 测试openpyxl
    import openpyxl
    print("openpyxl导入成功")
    
    # 测试我们的模块
    from utils.validators import ColumnConfigValidator
    print("validators导入成功")
    
    from core.excel_handler import ExcelHandler
    print("excel_handler导入成功")
    
    from core.mapper import DataMapper
    print("mapper导入成功")
    
    from core.data_processor import DataProcessor
    print("data_processor导入成功")
    
    print("所有导入成功！")
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
