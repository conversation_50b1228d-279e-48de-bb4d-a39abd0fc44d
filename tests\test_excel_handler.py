"""
测试ExcelHandler模块
"""

import pytest
import pandas as pd
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from core.excel_handler import ExcelHandler


class TestExcelHandler:
    """ExcelHandler测试类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.handler = ExcelHandler()

    def test_init(self):
        """测试初始化"""
        assert self.handler.supported_formats == ['.xlsx', '.xls']

    def test_validate_file_nonexistent(self):
        """测试验证不存在的文件"""
        is_valid, error_msg = self.handler.validate_file("nonexistent.xlsx")
        assert not is_valid
        assert "文件不存在" in error_msg

    def test_validate_file_unsupported_format(self, temp_dir):
        """测试验证不支持的文件格式"""
        # 创建一个txt文件
        txt_file = temp_dir / "test.txt"
        txt_file.write_text("test content")

        is_valid, error_msg = self.handler.validate_file(str(txt_file))
        assert not is_valid
        assert "不支持的文件格式" in error_msg

    def test_validate_file_valid(self, sample_data_source_file):
        """测试验证有效文件"""
        is_valid, error_msg = self.handler.validate_file(sample_data_source_file)
        assert is_valid
        assert error_msg == ""

    def test_get_worksheet_names(self, sample_data_source_file):
        """测试获取工作表名称"""
        sheet_names = self.handler.get_worksheet_names(sample_data_source_file)
        assert "过滤条件" in sheet_names
        assert "表格" in sheet_names
        assert len(sheet_names) == 2

    def test_get_worksheet_names_invalid_file(self):
        """测试获取不存在文件的工作表名称"""
        sheet_names = self.handler.get_worksheet_names("nonexistent.xlsx")
        assert sheet_names == []

    def test_get_column_names(self, sample_data_source_file):
        """测试获取列名"""
        columns = self.handler.get_column_names(sample_data_source_file, "表格")
        expected_columns = ["产品代码", "产品名称", "销量", "价格"]
        assert all(col in columns for col in expected_columns)

    def test_get_column_names_invalid_sheet(self, sample_data_source_file):
        """测试获取无效工作表的列名"""
        columns = self.handler.get_column_names(sample_data_source_file, "不存在的工作表")
        assert columns == []

    def test_validate_data_source_structure_valid(self, sample_data_source_file):
        """测试验证有效的数据源文件结构"""
        is_valid, error_msg = self.handler.validate_data_source_structure(sample_data_source_file)
        assert is_valid
        assert error_msg == ""

    def test_validate_data_source_structure_invalid(self, invalid_data_source_file):
        """测试验证无效的数据源文件结构"""
        is_valid, error_msg = self.handler.validate_data_source_structure(invalid_data_source_file)
        assert not is_valid
        assert "缺少必要的工作表" in error_msg

    def test_extract_date_from_filter_sheet(self, sample_data_source_file):
        """测试从过滤条件工作表提取日期"""
        success, error_msg, date = self.handler.extract_date_from_filter_sheet(sample_data_source_file)
        assert success
        assert error_msg == ""
        assert date == "2025-01-15"

    def test_extract_date_from_filter_sheet_invalid_file(self):
        """测试从无效文件提取日期"""
        success, error_msg, date = self.handler.extract_date_from_filter_sheet("nonexistent.xlsx")
        assert not success
        assert date is None

    def test_read_data_from_table_sheet(self, sample_data_source_file):
        """测试从表格工作表读取数据"""
        data_columns = ["产品名称"]
        key_column = "产品代码"

        success, error_msg, df = self.handler.read_data_from_table_sheet(
            sample_data_source_file, data_columns, key_column
        )

        assert success
        assert error_msg == ""
        assert df is not None
        assert len(df) == 4
        assert "产品代码" in df.columns
        assert "产品名称" in df.columns

    def test_read_data_from_table_sheet_missing_column(self, sample_data_source_file):
        """测试读取不存在的列"""
        data_columns = ["不存在的列"]
        key_column = "产品代码"

        success, error_msg, df = self.handler.read_data_from_table_sheet(
            sample_data_source_file, data_columns, key_column
        )

        assert not success
        assert "缺少列" in error_msg
        assert df is None

    def test_read_mapping_data(self, sample_mapping_file):
        """测试读取映射数据"""
        success, error_msg, mapping_dict = self.handler.read_mapping_data(
            sample_mapping_file, "Sheet1", "接口标识", "云序号"
        )

        assert success
        assert error_msg == ""
        assert mapping_dict is not None
        assert len(mapping_dict) == 3
        assert mapping_dict["A001"] == "CLOUD001"
        assert mapping_dict["A002"] == "CLOUD002"
        assert mapping_dict["A003"] == "CLOUD003"

    def test_read_mapping_data_missing_column(self, sample_mapping_file):
        """测试读取映射数据时缺少列"""
        success, error_msg, mapping_dict = self.handler.read_mapping_data(
            sample_mapping_file, "Sheet1", "不存在的列", "云序号"
        )

        assert not success
        assert "缺少主键列" in error_msg
        assert mapping_dict is None

    def test_write_output_file(self, temp_dir):
        """测试写入输出文件"""
        # 创建测试数据
        test_data = pd.DataFrame({
            "周期": ["2025-01-15", "2025-01-15"],
            "云序号": ["CLOUD001", "CLOUD002"],
            "标准产品名": ["产品A", "产品B"]
        })

        output_path = temp_dir / "output.xlsx"
        success, error_msg = self.handler.write_output_file(test_data, str(output_path))

        assert success
        assert error_msg == ""
        assert output_path.exists()

        # 验证写入的数据
        df_read = pd.read_excel(output_path, sheet_name='处理结果')
        assert len(df_read) == 2
        assert "周期" in df_read.columns
        assert "云序号" in df_read.columns
        assert "标准产品名" in df_read.columns
