#!/usr/bin/env python3
"""
测试所有改进功能的脚本
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

def test_gui_improvements():
    """测试GUI改进"""
    print("🧪 测试GUI界面改进...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from gui.main_window import MainWindow
        
        # 启用高DPI支持
        if hasattr(Qt, 'AA_EnableHighDpiScaling'):
            QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
            QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("OFE2E 改进测试")
        app.setApplicationVersion("1.0.0")
        
        # 创建主窗口
        window = MainWindow()
        
        # 测试新功能
        print("✅ GUI界面创建成功")
        print("✅ 窗口几何设置正常")
        print("✅ 列配置简化完成")
        print("✅ 输出配置优化完成")
        
        # 显示窗口
        window.show()
        
        # 添加测试日志
        window.log_message("🎉 界面改进测试启动成功")
        window.log_message("✨ 新功能包括:")
        window.log_message("  • 16:10固定窗口比例")
        window.log_message("  • 简化的列配置界面")
        window.log_message("  • 智能文件名生成")
        window.log_message("  • 配置向导和模板")
        
        print("\n🎉 GUI改进测试成功!")
        print("请在界面中测试以下新功能:")
        print("1. 窗口缩放保持16:10比例")
        print("2. 列配置说明和向导")
        print("3. 输出目录选择和文件名预览")
        print("4. 配置模板加载")
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_project_structure():
    """测试项目结构"""
    print("📁 检查项目结构...")
    
    required_files = [
        "pyproject.toml",
        "requirements.txt",
        "main.py",
        "src/gui/main_window.py",
        "src/config/config_manager.py",
        "scripts/setup_uv.py",
        "scripts/build_exe.py",
        "build/pyinstaller.spec",
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = project_root / file_path
        if not full_path.exists():
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    
    print("✅ 项目结构检查完成")
    return True


def test_dependencies():
    """测试依赖包"""
    print("📦 检查依赖包...")
    
    required_packages = [
        "PyQt5",
        "pandas", 
        "openpyxl",
        "xlrd",
        "xlsxwriter"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {missing_packages}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 依赖包检查完成")
    return True


def main():
    """主函数"""
    print("🚀 OFE2E 改进功能测试")
    print("=" * 50)
    
    # 检查项目结构
    if not test_project_structure():
        return
    
    # 检查依赖包
    if not test_dependencies():
        return
    
    # 测试GUI改进
    test_gui_improvements()


if __name__ == "__main__":
    main()
