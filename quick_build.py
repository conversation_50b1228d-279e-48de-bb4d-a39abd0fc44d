#!/usr/bin/env python3
"""
OFE2E 快速打包脚本
简化版本，快速打包成可执行文件
"""

import os
import sys
import subprocess
from pathlib import Path

def quick_build():
    """快速构建可执行文件"""
    print("🚀 OFE2E 快速打包")
    print("=" * 30)
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print("✅ PyInstaller 已安装")
    except ImportError:
        print("❌ PyInstaller 未安装")
        print("请运行: pip install pyinstaller")
        return False
    
    # 构建命令
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个文件
        "--windowed",                   # 无控制台窗口
        "--name=OFE2E",                # 可执行文件名
        "--add-data=src;src",          # 包含src目录
        "--add-data=config;config",    # 包含config目录
        "--add-data=docs;docs",        # 包含docs目录
        "--hidden-import=PyQt5.QtCore",
        "--hidden-import=PyQt5.QtGui",
        "--hidden-import=PyQt5.QtWidgets",
        "--hidden-import=pandas",
        "--hidden-import=openpyxl",
        "--exclude-module=matplotlib",
        "--exclude-module=scipy",
        "--exclude-module=tkinter",
        "main.py"
    ]
    
    print("🔨 开始打包...")
    print(f"命令: {' '.join(cmd[:3])} ...")
    
    try:
        result = subprocess.run(cmd, check=True)
        print("✅ 打包成功!")
        print("📁 输出文件: dist/OFE2E.exe")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⚠️ 用户取消打包")
        return False

if __name__ == "__main__":
    success = quick_build()
    if success:
        print("\n🎉 打包完成!")
        print("可以在 dist 目录找到 OFE2E.exe 文件")
    else:
        print("\n💡 提示:")
        print("1. 确保已安装 PyInstaller: pip install pyinstaller")
        print("2. 确保已安装所有依赖: pip install PyQt5 pandas openpyxl")
        print("3. 在项目根目录运行此脚本")
    
    input("\n按回车键退出...")
