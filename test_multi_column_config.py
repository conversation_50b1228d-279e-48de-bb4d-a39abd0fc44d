#!/usr/bin/env python3
"""
测试多列配置功能
演示如何配置和使用多个数据列
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.data_processor import ProcessingConfig
from utils.validators import ColumnConfigValidator


def test_multi_column_configuration():
    """测试多列配置功能"""
    print("🧪 测试多列配置功能")
    print("=" * 50)
    
    # 创建配置对象
    config = ProcessingConfig()
    
    print("📋 1. 单列配置（向后兼容）")
    print("-" * 30)
    
    # 单列配置
    config.data_column = "UUID[云序号]"
    print(f"设置单列: {config.data_column}")
    print(f"data_columns: {config.data_columns}")
    print(f"字符串表示: {config.get_data_columns_string()}")
    
    print("\n📋 2. 多列配置（新功能）")
    print("-" * 30)
    
    # 多列配置
    multi_column_config = "UUID[云序号];名称[产品名称];规格[产品规格];状态"
    config.set_data_columns_from_string(multi_column_config)
    
    print(f"设置多列: {multi_column_config}")
    print(f"data_columns: {config.data_columns}")
    print(f"data_column (第一列): {config.data_column}")
    print(f"字符串表示: {config.get_data_columns_string()}")
    
    print("\n🔍 3. 验证各列配置格式")
    print("-" * 30)
    
    for i, col_config in enumerate(config.data_columns):
        is_valid, error_msg, original_name, new_name = ColumnConfigValidator.validate_column_format(col_config)
        status = "✅" if is_valid else "❌"
        print(f"列{i+1}: {col_config}")
        print(f"  {status} 有效性: {'有效' if is_valid else '无效'}")
        if is_valid:
            print(f"     原列名: {original_name}")
            print(f"     新列名: {new_name or '(保持原名)'}")
        else:
            print(f"     错误: {error_msg}")
        print()
    
    print("📊 4. 配置验证")
    print("-" * 30)
    
    # 设置完整配置进行验证
    config.data_files = ["test1.xlsx", "test2.xlsx"]
    config.mapping_file = "mapping.xlsx"
    config.mapping_sheet = "Sheet1"
    config.mapping_key_column = "接口标识"
    config.mapping_value_column = "云序号[云序号]"
    config.data_key_column = "UUID"
    config.output_directory = "output"
    
    is_valid, errors = config.validate()
    print(f"配置有效性: {'✅ 有效' if is_valid else '❌ 无效'}")
    if errors:
        print("错误信息:")
        for error in errors:
            print(f"  - {error}")
    
    print("\n🎯 5. 支持的配置格式示例")
    print("-" * 30)
    
    examples = [
        "UUID[云序号]",  # 单列重命名
        "UUID",  # 单列保持原名
        "UUID[云序号];名称[产品名称];规格",  # 多列混合
        "产品代码[标准代码];产品名称[标准名称];价格[单价];库存[数量]",  # 多列全重命名
        "序号;名称;规格;状态;创建时间",  # 多列保持原名
    ]
    
    print("支持的配置格式:")
    for example in examples:
        print(f"  📝 {example}")
        
        # 解析示例
        columns = [col.strip() for col in example.split(';') if col.strip()]
        print(f"     解析为 {len(columns)} 列:")
        for col in columns:
            is_valid, _, original, new = ColumnConfigValidator.validate_column_format(col)
            if is_valid:
                print(f"       - {original} → {new or original}")
        print()


def test_column_format_validation():
    """测试列格式验证"""
    print("🔍 测试列格式验证")
    print("=" * 50)
    
    test_cases = [
        # 有效格式
        ("UUID[云序号]", True, "重命名格式"),
        ("UUID", True, "保持原名格式"),
        ("产品名称[标准产品名]", True, "中文列名重命名"),
        ("Product_Code[产品代码]", True, "英文到中文"),
        ("序号[ID]", True, "简单重命名"),
        
        # 无效格式
        ("", False, "空字符串"),
        ("UUID[", False, "未闭合括号"),
        ("UUID[]", False, "空新列名"),
        ("[新名称]", False, "缺少原列名"),
        ("UUID[新名称[子名称]]", False, "嵌套括号"),
        ("UUID[新名称]额外文本", False, "括号后有额外文本"),
    ]
    
    print("测试用例:")
    for test_input, expected_valid, description in test_cases:
        is_valid, error_msg, original, new = ColumnConfigValidator.validate_column_format(test_input)
        status = "✅" if is_valid == expected_valid else "❌"
        
        print(f"{status} {description}")
        print(f"   输入: '{test_input}'")
        print(f"   期望: {'有效' if expected_valid else '无效'}")
        print(f"   实际: {'有效' if is_valid else '无效'}")
        
        if is_valid:
            print(f"   解析: {original} → {new or '(保持原名)'}")
        else:
            print(f"   错误: {error_msg}")
        print()


if __name__ == "__main__":
    test_multi_column_configuration()
    print("\n" + "=" * 60 + "\n")
    test_column_format_validation()
    
    print("\n🎉 多列配置功能测试完成！")
    print("\n📖 使用说明:")
    print("1. 单列配置: config.data_column = 'UUID[云序号]'")
    print("2. 多列配置: config.set_data_columns_from_string('UUID[云序号];名称[产品名称];规格')")
    print("3. 格式: 原列名[新列名] 或 原列名")
    print("4. 分隔符: 多列用分号(;)分隔")
