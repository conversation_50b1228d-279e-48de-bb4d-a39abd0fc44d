"""
主窗口模块
提供Excel数据映射工具的主界面
"""

import sys
import os
import json
from datetime import datetime
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QGridLayout, QLabel, QPushButton, QLineEdit,
                             QTextEdit, QFileDialog, QGroupBox,
                             QProgressBar, QMessageBox, QSplitter, QFrame,
                             QScrollArea, QListWidget,
                             QCheckBox)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from utils.validators import ColumnConfigValidator, WorksheetValidator
from config.display_settings import get_optimal_resolution_for_screen, validate_resolution
from core.data_processor import DataProcessor, ProcessingConfig


class ProcessingThread(QThread):
    """数据处理线程"""

    # 信号定义
    progress_updated = pyqtSignal(int, str)  # 进度更新信号 (progress, message)
    log_message = pyqtSignal(str)  # 日志消息信号
    processing_finished = pyqtSignal(object)  # 处理完成信号 (result)

    def __init__(self, config):
        super().__init__()
        self.config = config
        self.processor = DataProcessor()

    def run(self):
        """执行数据处理"""
        try:
            # 执行处理
            result = self.processor.process_data(
                self.config,
                progress_callback=self.progress_updated.emit,
                log_callback=self.log_message.emit
            )

            # 发送完成信号
            self.processing_finished.emit(result)

        except Exception as e:
            # 发送错误信息
            self.log_message.emit(f"处理过程中发生异常: {str(e)}")
            self.processing_finished.emit(None)

    def stop_processing(self):
        """停止处理"""
        if hasattr(self, 'processor'):
            self.processor.stop_processing()


class MainWindow(QMainWindow):
    """主窗口类"""

    def __init__(self):
        super().__init__()
        self.processing_thread = None  # 处理线程
        self.init_ui()
        self.setup_connections()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("OFE2E - Excel数据映射工具")

        # 设置窗口尺寸和比例
        self.setup_window_geometry()

        # 设置最小窗口尺寸，防止界面元素重叠
        self.setMinimumSize(1000, 700)

        # 设置中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # 创建标题
        self.create_title_section(main_layout)

        # 创建主要内容区域
        content_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(content_splitter)

        # 左侧配置面板
        left_panel = self.create_left_panel()
        content_splitter.addWidget(left_panel)

        # 右侧预览和日志面板
        right_panel = self.create_right_panel()
        content_splitter.addWidget(right_panel)

        # 设置分割器比例
        content_splitter.setSizes([600, 400])

        # 底部状态和操作区域
        self.create_bottom_section(main_layout)

        # 应用样式
        self.apply_styles()

        # 显示欢迎信息和分辨率信息
        self.log_message("欢迎使用Excel数据映射工具!")
        if hasattr(self, 'resolution_info'):
            self.log_message(self.resolution_info["message"])
            self.log_message(self.resolution_info["screen_info"])
        self.log_message("请按照步骤配置数据源、映射源和输出设置")

    def create_title_section(self, layout):
        """创建标题区域"""
        title_frame = QFrame()
        title_frame.setFrameStyle(QFrame.StyledPanel)
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4a90e2, stop:1 #357abd);
                border-radius: 8px;
                margin: 5px;
            }
        """)

        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(20, 15, 20, 15)

        title_label = QLabel("Excel数据映射工具")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
                background: transparent;
            }
        """)

        subtitle_label = QLabel("支持多数据源处理和键值映射转换")
        subtitle_label.setStyleSheet("""
            QLabel {
                color: #e8f4fd;
                font-size: 14px;
                background: transparent;
            }
        """)

        title_layout.addWidget(title_label)
        title_layout.addStretch()
        title_layout.addWidget(subtitle_label)

        layout.addWidget(title_frame)

    def create_left_panel(self):
        """创建左侧配置面板"""
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        panel_widget = QWidget()
        panel_layout = QVBoxLayout(panel_widget)
        panel_layout.setSpacing(15)

        # 数据源配置组（重构后包含列配置）
        self.create_data_source_group(panel_layout)

        # 映射源配置组（重构后简化）
        self.create_mapping_source_group(panel_layout)

        # 输出配置组
        self.create_output_config_group(panel_layout)

        panel_layout.addStretch()
        scroll_area.setWidget(panel_widget)

        return scroll_area

    def create_data_source_group(self, layout):
        """创建数据源配置组（包含列配置）"""
        group = QGroupBox("📁 数据源配置")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #ddd;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
            }
        """)

        group_layout = QVBoxLayout(group)
        group_layout.setSpacing(15)

        # 数据源文件列表
        files_section = QVBoxLayout()
        files_label = QLabel("选择数据源文件:")
        files_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #333;
                font-size: 12px;
            }
        """)

        self.data_files_list = QListWidget()
        self.data_files_list.setMaximumHeight(100)
        self.data_files_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #ccc;
                border-radius: 4px;
                background-color: #fafafa;
                font-size: 11px;
            }
        """)

        # 文件操作按钮
        file_buttons_layout = QHBoxLayout()
        self.add_files_btn = QPushButton("添加文件")
        self.remove_files_btn = QPushButton("移除选中")
        self.clear_files_btn = QPushButton("清空列表")

        for btn in [self.add_files_btn, self.remove_files_btn, self.clear_files_btn]:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #f0f0f0;
                    border: 1px solid #ccc;
                    border-radius: 4px;
                    padding: 4px 8px;
                    font-size: 11px;
                }
                QPushButton:hover {
                    background-color: #e0e0e0;
                }
                QPushButton:pressed {
                    background-color: #d0d0d0;
                }
            """)

        file_buttons_layout.addWidget(self.add_files_btn)
        file_buttons_layout.addWidget(self.remove_files_btn)
        file_buttons_layout.addWidget(self.clear_files_btn)
        file_buttons_layout.addStretch()

        files_section.addWidget(files_label)
        files_section.addWidget(self.data_files_list)
        files_section.addLayout(file_buttons_layout)

        # 添加分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("QFrame { color: #ddd; }")

        # 数据源列配置
        config_section = QVBoxLayout()
        config_section.setSpacing(12)

        # 配置说明
        help_label = QLabel("""
        <b>数据源列配置：</b><br>
        • <b>数据列</b>：支持单列或多列配置<br>
        &nbsp;&nbsp;- 单列：格式为 "原列名[新列名]"，如 "产品名称[标准产品名]"<br>
        &nbsp;&nbsp;- 多列：用分号分隔，如 "UUID[云序号];名称[产品名称];规格"<br>
        • <b>主键列</b>：格式为 "列名称"，如 "产品代码"，用于与映射表匹配
        """)
        help_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                color: #495057;
                font-size: 10px;
            }
        """)
        help_label.setWordWrap(True)

        # 数据列配置
        data_col_layout = QGridLayout()
        data_col_layout.setSpacing(8)

        data_col_label = QLabel("数据列:")
        data_col_label.setToolTip("要提取的数据列，格式：原列名[新列名]")
        data_col_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #333;
                font-size: 12px;
            }
        """)

        self.data_column_edit = QLineEdit()
        self.data_column_edit.setPlaceholderText("例如: 产品名称[标准产品名] 或 UUID[云序号];名称[产品名称];规格")
        self.data_column_edit.textChanged.connect(self.validate_data_column_format)

        self.data_column_status = QLabel("✓")
        self.data_column_status.setStyleSheet("QLabel { color: #28a745; font-weight: bold; }")
        self.data_column_status.setVisible(False)

        # 主键列配置
        key_col_label = QLabel("主键列:")
        key_col_label.setToolTip("用于匹配的主键列，格式：列名称")
        key_col_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #333;
                font-size: 12px;
            }
        """)

        self.data_key_column_edit = QLineEdit()
        self.data_key_column_edit.setPlaceholderText("例如: 产品代码")
        self.data_key_column_edit.textChanged.connect(self.validate_data_key_column_format)

        self.data_key_column_status = QLabel("✓")
        self.data_key_column_status.setStyleSheet("QLabel { color: #28a745; font-weight: bold; }")
        self.data_key_column_status.setVisible(False)

        # 设置输入框样式
        input_style = """
            QLineEdit {
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 6px;
                background-color: white;
                font-size: 11px;
            }
            QLineEdit:focus {
                border-color: #4a90e2;
                border-width: 2px;
            }
            QLineEdit[valid="true"] {
                border-color: #28a745;
            }
            QLineEdit[valid="false"] {
                border-color: #dc3545;
            }
        """

        self.data_column_edit.setStyleSheet(input_style)
        self.data_key_column_edit.setStyleSheet(input_style)

        data_col_layout.addWidget(data_col_label, 0, 0)
        data_col_layout.addWidget(self.data_column_edit, 0, 1)
        data_col_layout.addWidget(self.data_column_status, 0, 2)

        data_col_layout.addWidget(key_col_label, 1, 0)
        data_col_layout.addWidget(self.data_key_column_edit, 1, 1)
        data_col_layout.addWidget(self.data_key_column_status, 1, 2)

        config_section.addWidget(help_label)
        config_section.addLayout(data_col_layout)

        group_layout.addLayout(files_section)
        group_layout.addWidget(separator)
        group_layout.addLayout(config_section)

        layout.addWidget(group)

    def create_mapping_source_group(self, layout):
        """创建映射源配置组（重构简化）"""
        group = QGroupBox("🗝️ 映射源配置")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #ddd;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
            }
        """)

        group_layout = QVBoxLayout(group)
        group_layout.setSpacing(15)

        # 映射文件选择
        file_section = QVBoxLayout()
        file_section.setSpacing(8)

        mapping_label = QLabel("映射文件:")
        mapping_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #333;
                font-size: 12px;
            }
        """)

        file_layout = QHBoxLayout()
        self.mapping_file_edit = QLineEdit()
        self.mapping_file_edit.setPlaceholderText("选择包含键值映射的Excel文件...")
        self.mapping_file_edit.textChanged.connect(self.on_mapping_file_changed)

        self.mapping_file_btn = QPushButton("📁 浏览")
        self.mapping_file_btn.setStyleSheet("""
            QPushButton {
                background-color: #4a90e2;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #357abd;
            }
        """)

        file_layout.addWidget(self.mapping_file_edit)
        file_layout.addWidget(self.mapping_file_btn)

        file_section.addWidget(mapping_label)
        file_section.addLayout(file_layout)

        # 添加分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("QFrame { color: #ddd; }")

        # 映射配置
        config_section = QVBoxLayout()
        config_section.setSpacing(12)

        # 配置说明
        help_label = QLabel("""
        <b>映射源配置：</b><br>
        • <b>工作表</b>：映射数据所在的工作表名称<br>
        • <b>主键列</b>：映射表中用于匹配的主键列名，如 "接口标识"<br>
        • <b>转换列</b>：映射表中用于转换的数据列，格式为 "原列名[新列名]"，如 "云序号[云序号]"
        """)
        help_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                color: #495057;
                font-size: 10px;
            }
        """)
        help_label.setWordWrap(True)

        # 配置表单
        config_form = QGridLayout()
        config_form.setSpacing(8)

        # 工作表配置（改为输入框）
        sheet_label = QLabel("工作表:")
        sheet_label.setToolTip("映射数据所在的工作表名称")
        sheet_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #333;
                font-size: 12px;
            }
        """)

        self.mapping_sheet_edit = QLineEdit()
        self.mapping_sheet_edit.setPlaceholderText("例如: Sheet1, 映射表, 数据")
        self.mapping_sheet_edit.setText("Sheet1")  # 默认值
        self.mapping_sheet_edit.textChanged.connect(self.validate_mapping_sheet_format)

        self.mapping_sheet_status = QLabel("✓")
        self.mapping_sheet_status.setStyleSheet("QLabel { color: #28a745; font-weight: bold; }")

        # 主键列配置（修改为简单列名格式）
        key_label = QLabel("主键列:")
        key_label.setToolTip("映射表中用于匹配的主键列名")
        key_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #333;
                font-size: 12px;
            }
        """)

        self.mapping_key_edit = QLineEdit()
        self.mapping_key_edit.setPlaceholderText("例如: 接口标识")
        self.mapping_key_edit.textChanged.connect(self.validate_mapping_key_format)

        self.mapping_key_status = QLabel("✓")
        self.mapping_key_status.setStyleSheet("QLabel { color: #28a745; font-weight: bold; }")
        self.mapping_key_status.setVisible(False)

        # 转换列配置（新增）
        value_label = QLabel("转换列:")
        value_label.setToolTip("映射表中用于转换的数据列，格式：原列名[新列名]")
        value_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #333;
                font-size: 12px;
            }
        """)

        self.mapping_value_edit = QLineEdit()
        self.mapping_value_edit.setPlaceholderText("例如: 云序号[云序号]")
        self.mapping_value_edit.textChanged.connect(self.validate_mapping_value_format)

        self.mapping_value_status = QLabel("✓")
        self.mapping_value_status.setStyleSheet("QLabel { color: #28a745; font-weight: bold; }")
        self.mapping_value_status.setVisible(False)

        # 设置输入框样式
        input_style = """
            QLineEdit {
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 6px;
                background-color: white;
                font-size: 11px;
            }
            QLineEdit:focus {
                border-color: #4a90e2;
                border-width: 2px;
            }
            QLineEdit[valid="true"] {
                border-color: #28a745;
            }
            QLineEdit[valid="false"] {
                border-color: #dc3545;
            }
        """

        self.mapping_file_edit.setStyleSheet(input_style)
        self.mapping_sheet_edit.setStyleSheet(input_style)
        self.mapping_key_edit.setStyleSheet(input_style)
        self.mapping_value_edit.setStyleSheet(input_style)

        config_form.addWidget(sheet_label, 0, 0)
        config_form.addWidget(self.mapping_sheet_edit, 0, 1)
        config_form.addWidget(self.mapping_sheet_status, 0, 2)

        config_form.addWidget(key_label, 1, 0)
        config_form.addWidget(self.mapping_key_edit, 1, 1)
        config_form.addWidget(self.mapping_key_status, 1, 2)

        config_form.addWidget(value_label, 2, 0)
        config_form.addWidget(self.mapping_value_edit, 2, 1)
        config_form.addWidget(self.mapping_value_status, 2, 2)

        config_section.addWidget(help_label)
        config_section.addLayout(config_form)

        group_layout.addLayout(file_section)
        group_layout.addWidget(separator)
        group_layout.addLayout(config_section)

        layout.addWidget(group)

    def create_output_config_group(self, layout):
        """创建输出配置组"""
        group = QGroupBox("💾 输出配置")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #ddd;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
            }
        """)

        group_layout = QVBoxLayout(group)
        group_layout.setSpacing(15)

        # 添加说明文本
        help_label = QLabel("""
        <b>输出说明：</b><br>
        • 选择保存目录，程序将自动生成文件名<br>
        • 文件名格式：映射结果_YYYYMMDD_HHMMSS.xlsx
        """)
        help_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 10px;
                color: #495057;
                font-size: 11px;
            }
        """)
        help_label.setWordWrap(True)
        group_layout.addWidget(help_label)

        # 输出目录配置
        dir_layout = QGridLayout()
        dir_layout.setSpacing(10)

        output_dir_label = QLabel("保存目录:")
        output_dir_label.setToolTip("选择输出文件的保存目录")
        output_dir_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #333;
                font-size: 12px;
            }
        """)

        self.output_dir_edit = QLineEdit()
        self.output_dir_edit.setPlaceholderText("选择输出文件保存目录...")
        self.output_dir_edit.setReadOnly(True)  # 只读，通过按钮选择
        self.output_dir_edit.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 8px;
                background-color: #f8f9fa;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #4a90e2;
            }
        """)

        self.output_dir_btn = QPushButton("📁 选择目录")
        self.output_dir_btn.setToolTip("选择输出文件保存目录")
        self.output_dir_btn.setStyleSheet("""
            QPushButton {
                background-color: #4a90e2;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #357abd;
            }
        """)

        dir_layout.addWidget(output_dir_label, 0, 0)
        dir_layout.addWidget(self.output_dir_edit, 0, 1)
        dir_layout.addWidget(self.output_dir_btn, 0, 2)

        # 文件名预览
        preview_layout = QHBoxLayout()
        preview_label = QLabel("文件名预览:")
        preview_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #333;
                font-size: 12px;
            }
        """)

        self.filename_preview_label = QLabel("映射结果_20240101_120000.xlsx")
        self.filename_preview_label.setStyleSheet("""
            QLabel {
                background-color: #e9ecef;
                border: 1px solid #ced4da;
                border-radius: 4px;
                padding: 6px;
                color: #6c757d;
                font-family: 'Courier New', monospace;
                font-size: 11px;
            }
        """)

        preview_layout.addWidget(preview_label)
        preview_layout.addWidget(self.filename_preview_label)
        preview_layout.addStretch()

        # 输出选项
        options_layout = QHBoxLayout()
        options_label = QLabel("输出选项:")
        options_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #333;
                font-size: 12px;
            }
        """)

        self.include_header_cb = QCheckBox("包含表头")
        self.include_header_cb.setChecked(True)
        self.include_header_cb.setToolTip("在输出文件中包含列标题")

        self.auto_open_cb = QCheckBox("完成后打开文件")
        self.auto_open_cb.setChecked(True)
        self.auto_open_cb.setToolTip("处理完成后自动打开输出文件")

        # 设置复选框样式
        checkbox_style = """
            QCheckBox {
                color: #333;
                font-size: 12px;
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:unchecked {
                border: 1px solid #ccc;
                background-color: white;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                border: 1px solid #4a90e2;
                background-color: #4a90e2;
                border-radius: 3px;
            }
        """

        self.include_header_cb.setStyleSheet(checkbox_style)
        self.auto_open_cb.setStyleSheet(checkbox_style)

        options_layout.addWidget(options_label)
        options_layout.addWidget(self.include_header_cb)
        options_layout.addWidget(self.auto_open_cb)
        options_layout.addStretch()

        group_layout.addLayout(dir_layout)
        group_layout.addLayout(preview_layout)
        group_layout.addLayout(options_layout)

        layout.addWidget(group)

        # 更新文件名预览
        self.update_filename_preview()

    def create_right_panel(self):
        """创建右侧预览和日志面板"""
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setSpacing(10)

        # 预览区域
        preview_group = QGroupBox("📋 数据预览")
        preview_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #ddd;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
            }
        """)

        preview_layout = QVBoxLayout(preview_group)
        self.preview_text = QTextEdit()
        self.preview_text.setMaximumHeight(200)
        self.preview_text.setPlaceholderText("选择文件后将显示数据预览...")
        self.preview_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ccc;
                border-radius: 4px;
                background-color: #fafafa;
                font-family: 'Courier New', monospace;
                font-size: 12px;
            }
        """)
        preview_layout.addWidget(self.preview_text)

        # 日志区域
        log_group = QGroupBox("📝 处理日志")
        log_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #ddd;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
            }
        """)

        log_layout = QVBoxLayout(log_group)

        # 日志控制按钮
        log_controls = QHBoxLayout()
        self.clear_log_btn = QPushButton("清空日志")
        self.save_log_btn = QPushButton("保存日志")

        for btn in [self.clear_log_btn, self.save_log_btn]:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #f0f0f0;
                    border: 1px solid #ccc;
                    border-radius: 4px;
                    padding: 4px 8px;
                    font-size: 11px;
                }
                QPushButton:hover {
                    background-color: #e0e0e0;
                }
            """)

        log_controls.addWidget(self.clear_log_btn)
        log_controls.addWidget(self.save_log_btn)
        log_controls.addStretch()

        self.log_text = QTextEdit()
        self.log_text.setPlaceholderText("处理日志将显示在这里...")
        self.log_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ccc;
                border-radius: 4px;
                background-color: #f8f8f8;
                font-family: 'Courier New', monospace;
                font-size: 11px;
            }
        """)

        log_layout.addLayout(log_controls)
        log_layout.addWidget(self.log_text)

        right_layout.addWidget(preview_group)
        right_layout.addWidget(log_group)

        return right_widget

    def create_bottom_section(self, layout):
        """创建底部状态和操作区域"""
        bottom_frame = QFrame()
        bottom_frame.setFrameStyle(QFrame.StyledPanel)
        bottom_frame.setStyleSheet("""
            QFrame {
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 6px;
                margin: 5px;
            }
        """)

        bottom_layout = QHBoxLayout(bottom_frame)
        bottom_layout.setContentsMargins(15, 10, 15, 10)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #ccc;
                border-radius: 4px;
                text-align: center;
                background-color: white;
            }
            QProgressBar::chunk {
                background-color: #4a90e2;
                border-radius: 3px;
            }
        """)

        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 12px;
                padding: 4px;
            }
        """)

        # 配置操作按钮
        config_layout = QHBoxLayout()
        self.load_config_btn = QPushButton("加载配置")
        self.save_config_btn = QPushButton("保存配置")

        # 主要操作按钮
        self.start_btn = QPushButton("开始处理")
        self.stop_btn = QPushButton("停止处理")
        self.stop_btn.setEnabled(False)

        # 设置按钮样式
        for btn in [self.load_config_btn, self.save_config_btn]:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #6c757d;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: #5a6268;
                }
            """)

        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)

        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)

        config_layout.addWidget(self.load_config_btn)
        config_layout.addWidget(self.save_config_btn)

        bottom_layout.addWidget(self.status_label)
        bottom_layout.addWidget(self.progress_bar)
        bottom_layout.addStretch()
        bottom_layout.addLayout(config_layout)
        bottom_layout.addWidget(self.start_btn)
        bottom_layout.addWidget(self.stop_btn)

        layout.addWidget(bottom_frame)

    def setup_connections(self):
        """设置信号连接"""
        # 文件操作
        self.add_files_btn.clicked.connect(self.add_data_files)
        self.remove_files_btn.clicked.connect(self.remove_selected_files)
        self.clear_files_btn.clicked.connect(self.clear_file_list)

        # 映射文件操作
        self.mapping_file_btn.clicked.connect(self.select_mapping_file)

        # 输出目录操作
        self.output_dir_btn.clicked.connect(self.select_output_directory)

        # 配置操作
        self.load_config_btn.clicked.connect(self.load_configuration)
        self.save_config_btn.clicked.connect(self.save_configuration)

        # 处理操作
        self.start_btn.clicked.connect(self.start_processing)
        self.stop_btn.clicked.connect(self.stop_processing)

        # 日志操作
        self.clear_log_btn.clicked.connect(self.clear_log)
        self.save_log_btn.clicked.connect(self.save_log)

        # 文件列表选择变化
        self.data_files_list.itemSelectionChanged.connect(self.on_file_selection_changed)

    def apply_styles(self):
        """应用全局样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ffffff;
            }
            QLabel {
                color: #333333;
                font-size: 12px;
            }
            QCheckBox {
                color: #333333;
                font-size: 12px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:unchecked {
                border: 1px solid #ccc;
                background-color: white;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                border: 1px solid #4a90e2;
                background-color: #4a90e2;
                border-radius: 3px;
            }
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #f0f0f0;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #c0c0c0;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #a0a0a0;
            }
        """)

    # 事件处理方法
    def add_data_files(self):
        """添加数据源文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "选择数据源文件",
            "",
            "Excel文件 (*.xlsx *.xls);;所有文件 (*)"
        )

        for file_path in files:
            if file_path not in [self.data_files_list.item(i).text()
                               for i in range(self.data_files_list.count())]:
                self.data_files_list.addItem(file_path)
                self.log_message(f"添加数据源文件: {file_path}")

        self.update_preview()

    def remove_selected_files(self):
        """移除选中的文件"""
        current_row = self.data_files_list.currentRow()
        if current_row >= 0:
            item = self.data_files_list.takeItem(current_row)
            self.log_message(f"移除文件: {item.text()}")
            self.update_preview()

    def clear_file_list(self):
        """清空文件列表"""
        self.data_files_list.clear()
        self.preview_text.clear()
        self.log_message("清空文件列表")

    def select_mapping_file(self):
        """选择映射文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择映射文件",
            "",
            "Excel文件 (*.xlsx *.xls);;所有文件 (*)"
        )

        if file_path:
            self.mapping_file_edit.setText(file_path)
            self.log_message(f"选择映射文件: {file_path}")

    def select_output_directory(self):
        """选择输出目录"""
        directory = QFileDialog.getExistingDirectory(
            self,
            "选择输出目录",
            "",
            QFileDialog.ShowDirsOnly
        )

        if directory:
            self.output_dir_edit.setText(directory)
            self.log_message(f"设置输出目录: {directory}")
            self.update_filename_preview()

    def update_filename_preview(self):
        """更新文件名预览"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"映射结果_{timestamp}.xlsx"
        self.filename_preview_label.setText(filename)

    def generate_output_filename(self):
        """生成输出文件的完整路径"""
        output_dir = self.output_dir_edit.text().strip()
        if not output_dir:
            return ""

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"映射结果_{timestamp}.xlsx"
        return os.path.join(output_dir, filename)

    def open_config_wizard(self):
        """打开配置向导"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QDialogButtonBox

        dialog = QDialog(self)
        dialog.setWindowTitle("配置向导")
        dialog.setModal(True)
        dialog.resize(400, 300)

        layout = QVBoxLayout(dialog)

        help_text = QLabel("""
        <h3>配置向导</h3>
        <p><b>步骤1：选择数据源文件</b><br>
        选择包含需要处理数据的Excel文件</p>

        <p><b>步骤2：配置映射文件</b><br>
        选择包含键值映射关系的Excel文件</p>

        <p><b>步骤3：设置列配置</b><br>
        • 数据列名：要转换的数据所在的列<br>
        • 主键列名：用于匹配的唯一标识列<br>
        • 映射后列名：输出文件中的列标题</p>

        <p><b>步骤4：选择输出目录</b><br>
        选择结果文件的保存位置</p>

        <p><b>步骤5：开始处理</b><br>
        点击"开始处理"按钮执行数据映射</p>
        """)
        help_text.setWordWrap(True)
        help_text.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 15px;
                color: #495057;
            }
        """)

        layout.addWidget(help_text)

        buttons = QDialogButtonBox(QDialogButtonBox.Ok)
        buttons.accepted.connect(dialog.accept)
        layout.addWidget(buttons)

        dialog.exec_()

    def load_config_template(self):
        """加载配置模板"""
        from PyQt5.QtWidgets import QInputDialog

        templates = {
            "产品名称映射": {
                "data_column": "产品名称",
                "key_column": "产品代码",
                "output_mapped_column": "标准产品名称"
            },
            "客户信息映射": {
                "data_column": "客户名称",
                "key_column": "客户ID",
                "output_mapped_column": "规范客户名称"
            },
            "地区代码映射": {
                "data_column": "地区名称",
                "key_column": "地区代码",
                "output_mapped_column": "标准地区名称"
            }
        }

        template_names = list(templates.keys())
        template_name, ok = QInputDialog.getItem(
            self, "选择配置模板", "请选择一个配置模板:",
            template_names, 0, False
        )

        if ok and template_name:
            template = templates[template_name]
            # 更新为新的配置格式
            self.data_column_edit.setText(f"{template['data_column']}[{template['output_mapped_column']}]")
            self.data_key_column_edit.setText(template["key_column"])
            self.log_message(f"已加载配置模板: {template_name}")

    def on_mapping_file_changed(self):
        """映射文件路径改变时的处理"""
        file_path = self.mapping_file_edit.text().strip()
        if file_path and os.path.exists(file_path):
            self.log_message(f"映射文件已选择: {os.path.basename(file_path)}")
        else:
            if file_path:  # 如果有路径但文件不存在
                self.log_message(f"映射文件不存在: {file_path}", "WARNING")

    def on_file_selection_changed(self):
        """文件选择变化时更新预览"""
        self.update_preview()

    def update_preview(self):
        """更新数据预览"""
        current_item = self.data_files_list.currentItem()
        if current_item:
            file_path = current_item.text()
            try:
                # 这里应该读取Excel文件并显示预览
                # 暂时显示文件信息
                preview_text = f"文件: {os.path.basename(file_path)}\n"
                preview_text += f"路径: {file_path}\n"
                preview_text += f"大小: {os.path.getsize(file_path)} 字节\n"
                preview_text += "\n数据预览功能将在业务逻辑模块完成后实现..."

                self.preview_text.setPlainText(preview_text)
            except Exception as e:
                self.preview_text.setPlainText(f"预览失败: {str(e)}")
        else:
            self.preview_text.clear()

    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {level}: {message}"

        self.log_text.append(formatted_message)

        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.End)
        self.log_text.setTextCursor(cursor)

    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.log_message("日志已清空")

    def save_log(self):
        """保存日志"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "保存日志文件",
            f"log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            "文本文件 (*.txt);;所有文件 (*)"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.toPlainText())
                self.log_message(f"日志已保存到: {file_path}")
            except Exception as e:
                self.log_message(f"保存日志失败: {str(e)}", "ERROR")

    def load_configuration(self):
        """加载配置"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "加载配置文件",
            "",
            "JSON文件 (*.json);;所有文件 (*)"
        )

        if file_path:
            try:

                with open(file_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 加载配置到界面
                self.apply_config_to_ui(config)
                self.log_message(f"配置已加载: {file_path}")

            except Exception as e:
                self.log_message(f"加载配置失败: {str(e)}", "ERROR")
                QMessageBox.warning(self, "错误", f"加载配置失败:\n{str(e)}")

    def save_configuration(self):
        """保存配置"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "保存配置文件",
            f"config_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "JSON文件 (*.json);;所有文件 (*)"
        )

        if file_path:
            try:
                config = self.get_config_from_ui()

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)

                self.log_message(f"配置已保存: {file_path}")

            except Exception as e:
                self.log_message(f"保存配置失败: {str(e)}", "ERROR")
                QMessageBox.warning(self, "错误", f"保存配置失败:\n{str(e)}")

    def get_config_from_ui(self):
        """从界面获取配置"""
        config = {
            "data_files": [self.data_files_list.item(i).text()
                          for i in range(self.data_files_list.count())],
            "mapping_file": self.mapping_file_edit.text(),
            "mapping_sheet": self.mapping_sheet_edit.text(),
            "mapping_key_column": self.mapping_key_edit.text(),
            "mapping_value_column": self.mapping_value_edit.text(),
            "data_column": self.data_column_edit.text(),
            "data_key_column": self.data_key_column_edit.text(),
            "output_directory": self.output_dir_edit.text(),
            "include_header": self.include_header_cb.isChecked(),
            "auto_open_file": self.auto_open_cb.isChecked()
        }
        return config

    def apply_config_to_ui(self, config):
        """将配置应用到界面"""
        # 清空并加载数据文件
        self.data_files_list.clear()
        for file_path in config.get("data_files", []):
            self.data_files_list.addItem(file_path)

        # 设置映射文件
        self.mapping_file_edit.setText(config.get("mapping_file", ""))
        self.mapping_sheet_edit.setText(config.get("mapping_sheet", "Sheet1"))
        self.mapping_key_edit.setText(config.get("mapping_key_column", ""))
        self.mapping_value_edit.setText(config.get("mapping_value_column", ""))

        # 设置数据源列配置
        self.data_column_edit.setText(config.get("data_column", ""))
        self.data_key_column_edit.setText(config.get("data_key_column", ""))

        # 设置输出配置
        self.output_dir_edit.setText(config.get("output_directory", ""))
        self.include_header_cb.setChecked(config.get("include_header", True))
        self.auto_open_cb.setChecked(config.get("auto_open_file", True))

        # 更新文件名预览
        self.update_filename_preview()

    def start_processing(self):
        """开始处理"""
        # 验证配置
        if not self.validate_configuration():
            return

        self.log_message("开始数据处理...")
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("处理中...")

        # 创建处理配置
        config = self._create_processing_config()

        # 启动处理线程
        self.processing_thread = ProcessingThread(config)
        self.processing_thread.progress_updated.connect(self.on_progress_updated)
        self.processing_thread.log_message.connect(self.log_message)
        self.processing_thread.processing_finished.connect(self.on_processing_finished)
        self.processing_thread.start()

    def stop_processing(self):
        """停止处理"""
        self.log_message("用户取消处理")
        if self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.stop_processing()
            self.processing_thread.wait(3000)  # 等待最多3秒
        self.reset_ui_state()

    def on_progress_updated(self, progress, message):
        """处理进度更新"""
        self.progress_bar.setValue(progress)
        self.status_label.setText(message)

    def on_processing_finished(self, result):
        """处理完成回调"""
        self.reset_ui_state()

        if result is None:
            self.log_message("处理失败", "ERROR")
            QMessageBox.critical(self, "处理失败", "数据处理过程中发生错误，请查看日志了解详情。")
            return

        if result.success:
            self.log_message("处理完成")
            self.log_message(result.get_summary())

            # 显示处理结果
            QMessageBox.information(self, "处理完成",
                f"数据处理成功完成！\n\n"
                f"处理文件: {result.processed_files}/{result.total_files}\n"
                f"处理记录: {result.mapped_records}/{result.total_records}\n"
                f"输出文件: {len(result.output_files)}个\n"
                f"处理时间: {result.processing_time:.2f}秒")
        else:
            self.log_message("处理失败", "ERROR")
            error_details = "\n".join(result.errors) if result.errors else "未知错误"
            QMessageBox.warning(self, "处理失败", f"数据处理失败：\n\n{error_details}")

    def reset_ui_state(self):
        """重置UI状态"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.status_label.setText("就绪")

    def _create_processing_config(self):
        """创建处理配置"""
        config = ProcessingConfig()

        # 获取数据文件列表
        config.data_files = [self.data_files_list.item(i).text()
                           for i in range(self.data_files_list.count())]

        # 映射配置
        config.mapping_file = self.mapping_file_edit.text()
        config.mapping_sheet = self.mapping_sheet_edit.text()
        config.mapping_key_column = self.mapping_key_edit.text()
        config.mapping_value_column = self.mapping_value_edit.text()

        # 数据配置（支持多列）
        data_column_text = self.data_column_edit.text().strip()
        if data_column_text:
            # 检查是否为多列配置（包含分号）
            if ';' in data_column_text:
                config.set_data_columns_from_string(data_column_text)
            else:
                config.data_column = data_column_text

        config.data_key_column = self.data_key_column_edit.text()

        # 输出配置
        config.output_directory = self.output_dir_edit.text()
        config.include_header = self.include_header_cb.isChecked()

        return config

    def validate_configuration(self):
        """验证配置"""
        errors = []

        if self.data_files_list.count() == 0:
            errors.append("请至少选择一个数据源文件")

        if not self.mapping_file_edit.text().strip():
            errors.append("请选择映射文件")

        if not self.data_column_edit.text().strip():
            errors.append("请输入数据列配置")

        if not self.data_key_column_edit.text().strip():
            errors.append("请输入数据源主键列")

        if not self.mapping_sheet_edit.text().strip():
            errors.append("请输入映射工作表名称")

        if not self.mapping_key_edit.text().strip():
            errors.append("请输入映射主键列配置")

        if not self.mapping_value_edit.text().strip():
            errors.append("请输入映射转换列配置")

        if not self.output_dir_edit.text().strip():
            errors.append("请选择输出目录")

        if errors:
            error_message = "配置验证失败:\n" + "\n".join(f"• {error}" for error in errors)
            QMessageBox.warning(self, "配置错误", error_message)
            self.log_message("配置验证失败", "ERROR")
            return False

        return True

    def setup_window_geometry(self):
        """设置窗口几何属性 - 智能固定分辨率"""
        # 获取屏幕尺寸
        from PyQt5.QtWidgets import QApplication
        screen = QApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()
        screen_width = screen_geometry.width()
        screen_height = screen_geometry.height()

        # 根据屏幕尺寸选择最优分辨率
        optimal_config = get_optimal_resolution_for_screen(screen_width, screen_height)
        FIXED_WIDTH = optimal_config["width"]
        FIXED_HEIGHT = optimal_config["height"]

        # 验证分辨率并调整
        _, FIXED_WIDTH, FIXED_HEIGHT = validate_resolution(FIXED_WIDTH, FIXED_HEIGHT)

        # 如果屏幕太小，进一步调整
        if screen_width < FIXED_WIDTH or screen_height < FIXED_HEIGHT:
            FIXED_WIDTH = min(FIXED_WIDTH, int(screen_width * 0.95))
            FIXED_HEIGHT = min(FIXED_HEIGHT, int(screen_height * 0.95))

        # 计算居中位置
        x = max(0, (screen_width - FIXED_WIDTH) // 2)
        y = max(0, (screen_height - FIXED_HEIGHT) // 2)

        # 设置固定窗口尺寸和位置
        self.setGeometry(x, y, FIXED_WIDTH, FIXED_HEIGHT)

        # 设置固定尺寸（禁用缩放）
        self.setFixedSize(FIXED_WIDTH, FIXED_HEIGHT)

        # 记录窗口尺寸和配置信息
        self.fixed_width = FIXED_WIDTH
        self.fixed_height = FIXED_HEIGHT
        self.resolution_config = optimal_config

        # 记录分辨率信息（延迟到UI创建完成后）
        self.resolution_info = {
            "message": f"使用固定分辨率: {FIXED_WIDTH}x{FIXED_HEIGHT} ({optimal_config['name']})",
            "screen_info": f"屏幕尺寸: {screen_width}x{screen_height}"
        }

    # 验证方法
    def validate_data_column_format(self):
        """验证数据列格式（支持多列）"""
        text = self.data_column_edit.text()

        if text.strip():  # 只有在有输入时才显示状态
            # 检查是否为多列配置
            if ';' in text:
                # 多列配置验证
                columns = [col.strip() for col in text.split(';') if col.strip()]
                all_valid = True
                error_details = []
                valid_columns = []

                for i, col_config in enumerate(columns):
                    is_valid, error_msg, original_name, new_name = ColumnConfigValidator.validate_column_format(col_config)
                    if is_valid:
                        valid_columns.append(f"{original_name} → {new_name or original_name}")
                    else:
                        all_valid = False
                        error_details.append(f"列{i+1}: {error_msg}")

                if all_valid:
                    self.data_column_edit.setProperty("valid", "true")
                    self.data_column_status.setText("✓")
                    self.data_column_status.setStyleSheet("QLabel { color: #28a745; font-weight: bold; }")
                    self.data_column_status.setVisible(True)
                    tooltip = f"多列配置 ({len(columns)}列):\n" + "\n".join(valid_columns)
                    self.data_column_edit.setToolTip(tooltip)
                else:
                    self.data_column_edit.setProperty("valid", "false")
                    self.data_column_status.setText("✗")
                    self.data_column_status.setStyleSheet("QLabel { color: #dc3545; font-weight: bold; }")
                    self.data_column_status.setVisible(True)
                    self.data_column_edit.setToolTip("多列配置错误:\n" + "\n".join(error_details))
            else:
                # 单列配置验证
                is_valid, error_msg, original_name, new_name = ColumnConfigValidator.validate_column_format(text)
                if is_valid:
                    self.data_column_edit.setProperty("valid", "true")
                    self.data_column_status.setText("✓")
                    self.data_column_status.setStyleSheet("QLabel { color: #28a745; font-weight: bold; }")
                    self.data_column_status.setVisible(True)
                    self.data_column_edit.setToolTip(f"单列配置:\n原列名: {original_name}\n新列名: {new_name or '(保持原名)'}")
                else:
                    self.data_column_edit.setProperty("valid", "false")
                    self.data_column_status.setText("✗")
                    self.data_column_status.setStyleSheet("QLabel { color: #dc3545; font-weight: bold; }")
                    self.data_column_status.setVisible(True)
                    self.data_column_edit.setToolTip(error_msg)
        else:
            self.data_column_status.setVisible(False)
            self.data_column_edit.setProperty("valid", None)
            self.data_column_edit.setToolTip("要提取的数据列\n单列: 原列名[新列名]\n多列: 用分号分隔")

        self.data_column_edit.style().unpolish(self.data_column_edit)
        self.data_column_edit.style().polish(self.data_column_edit)

    def validate_data_key_column_format(self):
        """验证数据主键列格式"""
        text = self.data_key_column_edit.text()

        if text.strip():
            # 主键列只需要列名，不需要重命名
            if text.strip() and '[' not in text and ']' not in text:
                self.data_key_column_edit.setProperty("valid", "true")
                self.data_key_column_status.setText("✓")
                self.data_key_column_status.setStyleSheet("QLabel { color: #28a745; font-weight: bold; }")
                self.data_key_column_status.setVisible(True)
                self.data_key_column_edit.setToolTip(f"主键列: {text.strip()}")
            else:
                self.data_key_column_edit.setProperty("valid", "false")
                self.data_key_column_status.setText("✗")
                self.data_key_column_status.setStyleSheet("QLabel { color: #dc3545; font-weight: bold; }")
                self.data_key_column_status.setVisible(True)
                self.data_key_column_edit.setToolTip("主键列格式错误，只需要列名称，不需要方括号")
        else:
            self.data_key_column_status.setVisible(False)
            self.data_key_column_edit.setProperty("valid", None)
            self.data_key_column_edit.setToolTip("用于匹配的主键列，格式：列名称")

        self.data_key_column_edit.style().unpolish(self.data_key_column_edit)
        self.data_key_column_edit.style().polish(self.data_key_column_edit)

    def validate_mapping_sheet_format(self):
        """验证映射工作表格式"""
        text = self.mapping_sheet_edit.text()
        is_valid, error_msg = WorksheetValidator.validate_worksheet_name(text)

        if text.strip():
            if is_valid:
                self.mapping_sheet_edit.setProperty("valid", "true")
                self.mapping_sheet_status.setText("✓")
                self.mapping_sheet_status.setStyleSheet("QLabel { color: #28a745; font-weight: bold; }")
                self.mapping_sheet_status.setVisible(True)
                self.mapping_sheet_edit.setToolTip(f"工作表: {text}")
            else:
                self.mapping_sheet_edit.setProperty("valid", "false")
                self.mapping_sheet_status.setText("✗")
                self.mapping_sheet_status.setStyleSheet("QLabel { color: #dc3545; font-weight: bold; }")
                self.mapping_sheet_status.setVisible(True)
                self.mapping_sheet_edit.setToolTip(error_msg)
        else:
            self.mapping_sheet_status.setVisible(False)
            self.mapping_sheet_edit.setProperty("valid", None)
            self.mapping_sheet_edit.setToolTip("映射数据所在的工作表名称")

        self.mapping_sheet_edit.style().unpolish(self.mapping_sheet_edit)
        self.mapping_sheet_edit.style().polish(self.mapping_sheet_edit)

    def validate_mapping_key_format(self):
        """验证映射主键列格式（简单列名）"""
        text = self.mapping_key_edit.text()

        if text.strip():
            # 主键列只需要列名，不需要重命名格式
            if text.strip() and '[' not in text and ']' not in text:
                self.mapping_key_edit.setProperty("valid", "true")
                self.mapping_key_status.setText("✓")
                self.mapping_key_status.setStyleSheet("QLabel { color: #28a745; font-weight: bold; }")
                self.mapping_key_status.setVisible(True)
                self.mapping_key_edit.setToolTip(f"主键列: {text.strip()}")
            else:
                self.mapping_key_edit.setProperty("valid", "false")
                self.mapping_key_status.setText("✗")
                self.mapping_key_status.setStyleSheet("QLabel { color: #dc3545; font-weight: bold; }")
                self.mapping_key_status.setVisible(True)
                self.mapping_key_edit.setToolTip("主键列格式错误，只需要列名称，不需要方括号")
        else:
            self.mapping_key_status.setVisible(False)
            self.mapping_key_edit.setProperty("valid", None)
            self.mapping_key_edit.setToolTip("映射表中用于匹配的主键列名")

        self.mapping_key_edit.style().unpolish(self.mapping_key_edit)
        self.mapping_key_edit.style().polish(self.mapping_key_edit)

    def validate_mapping_value_format(self):
        """验证映射转换列格式"""
        text = self.mapping_value_edit.text()
        is_valid, error_msg, original_name, new_name = ColumnConfigValidator.validate_column_format(text)

        if text.strip():
            if is_valid:
                self.mapping_value_edit.setProperty("valid", "true")
                self.mapping_value_status.setText("✓")
                self.mapping_value_status.setStyleSheet("QLabel { color: #28a745; font-weight: bold; }")
                self.mapping_value_status.setVisible(True)
                self.mapping_value_edit.setToolTip(f"原列名: {original_name}\n新列名: {new_name or '(保持原名)'}")
            else:
                self.mapping_value_edit.setProperty("valid", "false")
                self.mapping_value_status.setText("✗")
                self.mapping_value_status.setStyleSheet("QLabel { color: #dc3545; font-weight: bold; }")
                self.mapping_value_status.setVisible(True)
                self.mapping_value_edit.setToolTip(error_msg)
        else:
            self.mapping_value_status.setVisible(False)
            self.mapping_value_edit.setProperty("valid", None)
            self.mapping_value_edit.setToolTip("映射表中用于转换的数据列，格式：原列名[新列名]")

        self.mapping_value_edit.style().unpolish(self.mapping_value_edit)
        self.mapping_value_edit.style().polish(self.mapping_value_edit)