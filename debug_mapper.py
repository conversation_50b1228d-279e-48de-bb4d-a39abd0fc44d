#!/usr/bin/env python3
"""
调试映射器问题
"""

import sys
import pandas as pd
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.excel_handler import ExcelHandler
from core.mapper import DataMapper


def debug_mapper():
    """调试映射器"""
    print("🔍 调试映射器问题")
    print("=" * 50)

    # 初始化
    excel_handler = ExcelHandler()
    mapper = DataMapper()

    # 文件路径
    data_file = "docs/box/input/数据源文件A.xlsx"
    mapping_file = "docs/box/input/映射文件A.xlsx"

    print(f"📊 读取数据文件: {data_file}")

    # 读取数据
    try:
        data_df = pd.read_excel(data_file, sheet_name="表格")
        print(f"   数据行数: {len(data_df)}")
        print(f"   数据列: {list(data_df.columns)}")

        # 检查UUID列的数据类型
        uuid_col = data_df['UUID']
        print(f"   UUID列类型: {type(uuid_col)}")
        print(f"   UUID列dtype: {uuid_col.dtype}")

        # 检查第一行数据
        first_row = data_df.iloc[0]
        print(f"   第一行类型: {type(first_row)}")
        print(f"   第一行UUID: {first_row['UUID']}")
        print(f"   第一行UUID类型: {type(first_row['UUID'])}")

        # 测试遍历
        print("\n🔍 测试数据遍历:")
        for i, (index, row) in enumerate(data_df.iterrows()):
            if i >= 2:  # 只测试前2行
                break
            print(f"   行{i}: index={index}, type(row)={type(row)}")
            key_value = row['UUID']
            print(f"   key_value: {key_value}, type: {type(key_value)}")

            # 测试pandas判断
            try:
                is_notna = pd.notna(key_value)
                print(f"   pd.notna(key_value): {is_notna}, type: {type(is_notna)}")
            except Exception as e:
                print(f"   pd.notna错误: {e}")

    except Exception as e:
        print(f"❌ 读取数据文件失败: {e}")
        return

    print(f"\n📋 读取映射文件: {mapping_file}")

    # 读取映射数据
    try:
        # 使用ExcelHandler读取映射数据
        success, error_msg, mapping_dict = excel_handler.read_mapping_data(
            mapping_file, "CVM - OMP", "接口标识", "云序号"
        )
        print(f"   映射数据读取: {'✅' if success else '❌'} {error_msg}")

        if success:
            print(f"   映射字典大小: {len(mapping_dict)}")
            print(f"   映射字典示例: {dict(list(mapping_dict.items())[:3])}")

            # 加载到映射器
            mapper.load_mapping(mapping_dict)
            print(f"   映射器加载完成")
        else:
            return

    except Exception as e:
        print(f"❌ 读取映射文件失败: {e}")
        import traceback
        traceback.print_exc()
        return

    print(f"\n🧪 测试映射过程:")

    # 手动测试映射逻辑
    try:
        key_column = "UUID"
        data_columns = ["UUID[UUID]"]
        output_value_column = "云序号"
        date_period = "2025-05-19"

        result_rows = []

        for i, (index, row) in enumerate(data_df.iterrows()):
            if i >= 2:  # 只测试前2行
                break

            print(f"\n   处理行{i}:")

            # 获取主键值
            key_value = row[key_column]
            print(f"     原始key_value: {key_value}, type: {type(key_value)}")

            # 确保key_value是标量值
            if hasattr(key_value, 'item') and hasattr(key_value, 'size') and key_value.size == 1:
                scalar_key = key_value.item()
                print(f"     使用.item(): {scalar_key}")
            elif hasattr(key_value, '__len__') and len(key_value) == 1:
                scalar_key = key_value[0] if hasattr(key_value, '__getitem__') else key_value
                print(f"     使用[0]: {scalar_key}")
            else:
                scalar_key = key_value
                print(f"     直接使用: {scalar_key}")

            print(f"     最终scalar_key: {scalar_key}, type: {type(scalar_key)}")

            # 测试pandas判断
            try:
                is_notna = pd.notna(scalar_key)
                print(f"     pd.notna(scalar_key): {is_notna}, type: {type(is_notna)}")

                # 测试字典查找
                in_dict = scalar_key in mapper.mapping_dict
                print(f"     scalar_key in mapping_dict: {in_dict}")

                # 测试组合条件
                condition = pd.notna(scalar_key) and scalar_key in mapper.mapping_dict
                print(f"     组合条件结果: {condition}")

            except Exception as e:
                print(f"     ❌ 条件判断错误: {e}")
                import traceback
                traceback.print_exc()
                break

    except Exception as e:
        print(f"❌ 映射测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    debug_mapper()
