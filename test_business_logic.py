#!/usr/bin/env python3
"""
测试业务逻辑模块
验证核心数据处理功能
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.excel_handler import ExcelHandler
from core.mapper import DataMapper
from core.data_processor import DataProcessor, ProcessingConfig
import pandas as pd


def test_excel_handler():
    """测试Excel处理器"""
    print("=== 测试Excel处理器 ===")
    
    handler = ExcelHandler()
    
    # 测试文件验证
    print("测试文件验证...")
    is_valid, error_msg = handler.validate_file("nonexistent.xlsx")
    print(f"不存在文件验证: {is_valid}, {error_msg}")
    
    # 如果有示例文件，可以测试
    sample_files = list(Path("docs/box/input").glob("*.xlsx"))
    if sample_files:
        sample_file = str(sample_files[0])
        print(f"测试文件: {sample_file}")
        
        is_valid, error_msg = handler.validate_file(sample_file)
        print(f"文件验证结果: {is_valid}, {error_msg}")
        
        if is_valid:
            sheet_names = handler.get_worksheet_names(sample_file)
            print(f"工作表列表: {sheet_names}")
            
            if sheet_names:
                columns = handler.get_column_names(sample_file, sheet_names[0])
                print(f"第一个工作表的列: {columns}")


def test_data_mapper():
    """测试数据映射器"""
    print("\n=== 测试数据映射器 ===")
    
    mapper = DataMapper()
    
    # 创建测试映射字典
    test_mapping = {
        "A001": "产品A",
        "A002": "产品B", 
        "A003": "产品C"
    }
    
    mapper.load_mapping(test_mapping)
    print(f"加载映射字典: {len(test_mapping)} 个映射关系")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        "产品代码": ["A001", "A002", "A004", "A003"],
        "销量": [100, 200, 150, 300]
    })
    
    print("测试数据:")
    print(test_data)
    
    # 测试映射覆盖率
    coverage_rate, uncovered_keys = mapper.validate_mapping_coverage(test_data, "产品代码")
    print(f"映射覆盖率: {coverage_rate:.1f}%")
    print(f"未覆盖的键: {uncovered_keys}")
    
    # 预览映射结果
    preview = mapper.preview_mapping_result(test_data, "产品代码", 3)
    print("映射预览:")
    for item in preview:
        print(f"  {item['key_value']} -> {item['mapped_value']} (找到: {item['mapping_found']})")


def test_processing_config():
    """测试处理配置"""
    print("\n=== 测试处理配置 ===")
    
    config = ProcessingConfig()
    
    # 测试空配置验证
    is_valid, errors = config.validate()
    print(f"空配置验证: {is_valid}")
    print(f"错误信息: {errors}")
    
    # 设置基本配置
    config.data_files = ["test1.xlsx", "test2.xlsx"]
    config.mapping_file = "mapping.xlsx"
    config.mapping_sheet = "Sheet1"
    config.mapping_key_column = "接口标识"
    config.mapping_value_column = "云序号[云序号]"
    config.data_column = "产品名称[标准产品名]"
    config.data_key_column = "产品代码"
    config.output_directory = "output"
    
    is_valid, errors = config.validate()
    print(f"完整配置验证: {is_valid}")
    if errors:
        print(f"错误信息: {errors}")


def create_test_data():
    """创建测试数据文件"""
    print("\n=== 创建测试数据 ===")
    
    # 确保测试目录存在
    test_dir = Path("test_data")
    test_dir.mkdir(exist_ok=True)
    
    # 创建测试数据源文件
    data_source = pd.DataFrame({
        "产品代码": ["A001", "A002", "A003", "A004"],
        "产品名称": ["产品A", "产品B", "产品C", "产品D"],
        "销量": [100, 200, 300, 150]
    })
    
    # 创建过滤条件工作表数据
    filter_data = pd.DataFrame({
        "条件": ["时间范围"],
        "值": ["(UTC+08:00)2025-01-15 08:00:00 ~ 2025-01-15 20:00:00"]
    })
    
    # 写入数据源文件
    data_file_path = test_dir / "test_data_source.xlsx"
    with pd.ExcelWriter(data_file_path, engine='openpyxl') as writer:
        filter_data.to_excel(writer, sheet_name='过滤条件', index=False)
        data_source.to_excel(writer, sheet_name='表格', index=False)
    
    print(f"创建测试数据源文件: {data_file_path}")
    
    # 创建映射文件
    mapping_data = pd.DataFrame({
        "接口标识": ["A001", "A002", "A003"],
        "云序号": ["CLOUD001", "CLOUD002", "CLOUD003"]
    })
    
    mapping_file_path = test_dir / "test_mapping.xlsx"
    with pd.ExcelWriter(mapping_file_path, engine='openpyxl') as writer:
        mapping_data.to_excel(writer, sheet_name='Sheet1', index=False)
    
    print(f"创建测试映射文件: {mapping_file_path}")
    
    return str(data_file_path), str(mapping_file_path)


def test_full_processing():
    """测试完整处理流程"""
    print("\n=== 测试完整处理流程 ===")
    
    # 创建测试数据
    data_file, mapping_file = create_test_data()
    
    # 创建处理配置
    config = ProcessingConfig()
    config.data_files = [data_file]
    config.mapping_file = mapping_file
    config.mapping_sheet = "Sheet1"
    config.mapping_key_column = "接口标识"
    config.mapping_value_column = "云序号[云序号]"
    config.data_column = "产品名称[标准产品名]"
    config.data_key_column = "产品代码"
    config.output_directory = "test_data/output"
    
    # 创建输出目录
    Path(config.output_directory).mkdir(parents=True, exist_ok=True)
    
    # 执行处理
    processor = DataProcessor()
    
    def progress_callback(progress, message):
        print(f"进度: {progress}% - {message}")
    
    def log_callback(message):
        print(f"日志: {message}")
    
    print("开始处理...")
    result = processor.process_data(config, progress_callback, log_callback)
    
    print("\n处理结果:")
    print(result.get_summary())
    
    if result.errors:
        print("错误信息:")
        for error in result.errors:
            print(f"  - {error}")
    
    if result.warnings:
        print("警告信息:")
        for warning in result.warnings:
            print(f"  - {warning}")


if __name__ == "__main__":
    print("OFE2E 业务逻辑测试")
    print("=" * 50)
    
    try:
        test_excel_handler()
        test_data_mapper()
        test_processing_config()
        test_full_processing()
        
        print("\n" + "=" * 50)
        print("所有测试完成！")
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
