#!/usr/bin/env python3
"""
简单的核心模块测试
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from core.excel_handler import ExcelHandler
        print("✓ ExcelHandler 导入成功")
    except Exception as e:
        print(f"✗ ExcelHandler 导入失败: {e}")
        return False
    
    try:
        from core.mapper import DataMapper
        print("✓ DataMapper 导入成功")
    except Exception as e:
        print(f"✗ DataMapper 导入失败: {e}")
        return False
    
    try:
        from core.data_processor import DataProcessor, ProcessingConfig
        print("✓ DataProcessor 导入成功")
    except Exception as e:
        print(f"✗ DataProcessor 导入失败: {e}")
        return False
    
    return True

def test_basic_functionality():
    """测试基本功能"""
    print("\n测试基本功能...")
    
    try:
        from core.excel_handler import ExcelHandler
        from core.mapper import DataMapper
        from core.data_processor import ProcessingConfig
        
        # 测试ExcelHandler
        handler = ExcelHandler()
        is_valid, error_msg = handler.validate_file("nonexistent.xlsx")
        print(f"✓ ExcelHandler 文件验证: {not is_valid} (预期为False)")
        
        # 测试DataMapper
        mapper = DataMapper()
        test_mapping = {"A": "1", "B": "2"}
        mapper.load_mapping(test_mapping)
        stats = mapper.get_mapping_statistics()
        print(f"✓ DataMapper 映射加载: {len(test_mapping)} 个映射")
        
        # 测试ProcessingConfig
        config = ProcessingConfig()
        is_valid, errors = config.validate()
        print(f"✓ ProcessingConfig 验证: {not is_valid} (预期为False，因为配置为空)")
        
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False

def test_validators():
    """测试验证器"""
    print("\n测试验证器...")
    
    try:
        from utils.validators import ColumnConfigValidator, WorksheetValidator
        
        # 测试列配置验证器
        is_valid, error_msg, original, new = ColumnConfigValidator.validate_column_format("产品名称[标准产品名]")
        print(f"✓ 列配置验证: {is_valid}, 原列名: {original}, 新列名: {new}")
        
        # 测试工作表验证器
        is_valid, error_msg = WorksheetValidator.validate_worksheet_name("Sheet1")
        print(f"✓ 工作表验证: {is_valid}")
        
        return True
        
    except Exception as e:
        print(f"✗ 验证器测试失败: {e}")
        return False

if __name__ == "__main__":
    print("OFE2E 核心模块测试")
    print("=" * 40)
    
    success = True
    
    success &= test_imports()
    success &= test_basic_functionality()
    success &= test_validators()
    
    print("\n" + "=" * 40)
    if success:
        print("✓ 所有测试通过！")
    else:
        print("✗ 部分测试失败")
