#!/usr/bin/env python3
"""
可执行文件打包脚本
支持PyInstaller和cx_Freeze两种打包方式
"""

import subprocess
import sys
import os
import shutil
from pathlib import Path
import argparse


def run_command(cmd, check=True):
    """运行命令并处理错误"""
    print(f"执行命令: {' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, check=check, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        if check:
            sys.exit(1)
        return e


def clean_build_dirs():
    """清理构建目录"""
    print("清理构建目录...")
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"已删除: {dir_name}")


def create_app_icon():
    """创建应用图标（如果不存在）"""
    icon_dir = Path("resources/icons")
    icon_dir.mkdir(parents=True, exist_ok=True)
    
    icon_path = icon_dir / "app.ico"
    if not icon_path.exists():
        print("创建默认应用图标...")
        # 这里可以添加创建默认图标的代码
        # 暂时创建一个占位符文件
        icon_path.touch()


def build_with_pyinstaller():
    """使用PyInstaller打包"""
    print("🔨 使用PyInstaller打包...")
    
    # 确保PyInstaller已安装
    try:
        run_command(["pyinstaller", "--version"])
    except:
        print("安装PyInstaller...")
        run_command(["pip", "install", "pyinstaller>=5.13.0"])
    
    # 创建图标
    create_app_icon()
    
    # 使用spec文件打包
    spec_file = Path("build/pyinstaller.spec")
    if spec_file.exists():
        run_command(["pyinstaller", str(spec_file), "--clean"])
    else:
        # 使用命令行参数打包
        cmd = [
            "pyinstaller",
            "--onefile",
            "--windowed",
            "--name", "OFE2E",
            "--add-data", "resources;resources",
            "--add-data", "config;config",
            "--hidden-import", "PyQt5.QtCore",
            "--hidden-import", "PyQt5.QtGui",
            "--hidden-import", "PyQt5.QtWidgets",
            "--hidden-import", "pandas",
            "--hidden-import", "openpyxl",
            "--exclude-module", "matplotlib",
            "--exclude-module", "tkinter",
            "main.py"
        ]
        
        # 添加图标（如果存在）
        icon_path = Path("resources/icons/app.ico")
        if icon_path.exists():
            cmd.extend(["--icon", str(icon_path)])
        
        run_command(cmd)
    
    print("✅ PyInstaller打包完成!")
    print("可执行文件位置: dist/OFE2E.exe")


def build_with_cx_freeze():
    """使用cx_Freeze打包"""
    print("🔨 使用cx_Freeze打包...")
    
    # 确保cx_Freeze已安装
    try:
        import cx_Freeze
    except ImportError:
        print("安装cx_Freeze...")
        run_command(["pip", "install", "cx-freeze>=6.15.0"])
    
    # 创建setup_cx.py文件
    setup_content = '''
import sys
from cx_Freeze import setup, Executable
from pathlib import Path

# 项目信息
project_root = Path(__file__).parent
src_path = project_root / "src"

# 包含的文件
include_files = [
    ("resources", "resources"),
    ("config", "config"),
]

# 构建选项
build_exe_options = {
    "packages": ["PyQt5", "pandas", "openpyxl", "xlrd", "xlsxwriter"],
    "excludes": ["matplotlib", "tkinter"],
    "include_files": include_files,
    "path": [str(src_path)],
}

# 可执行文件配置
exe = Executable(
    "main.py",
    base="Win32GUI" if sys.platform == "win32" else None,
    target_name="OFE2E.exe",
    icon="resources/icons/app.ico" if Path("resources/icons/app.ico").exists() else None,
)

setup(
    name="OFE2E",
    version="1.0.0",
    description="OFE2E - Excel数据映射工具",
    options={"build_exe": build_exe_options},
    executables=[exe],
)
'''
    
    with open("setup_cx.py", "w", encoding="utf-8") as f:
        f.write(setup_content)
    
    # 执行打包
    run_command([sys.executable, "setup_cx.py", "build"])
    
    print("✅ cx_Freeze打包完成!")
    print("可执行文件位置: build/exe.*/OFE2E.exe")


def test_executable():
    """测试可执行文件"""
    print("🧪 测试可执行文件...")
    
    exe_paths = [
        Path("dist/OFE2E.exe"),
        Path("build").glob("exe.*/OFE2E.exe"),
    ]
    
    for exe_path in exe_paths:
        if isinstance(exe_path, Path) and exe_path.exists():
            print(f"找到可执行文件: {exe_path}")
            print(f"文件大小: {exe_path.stat().st_size / 1024 / 1024:.1f} MB")
            return True
        elif hasattr(exe_path, '__iter__'):
            for path in exe_path:
                if path.exists():
                    print(f"找到可执行文件: {path}")
                    print(f"文件大小: {path.stat().st_size / 1024 / 1024:.1f} MB")
                    return True
    
    print("❌ 未找到可执行文件")
    return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="OFE2E可执行文件打包工具")
    parser.add_argument(
        "--tool", 
        choices=["pyinstaller", "cx_freeze", "both"], 
        default="pyinstaller",
        help="选择打包工具"
    )
    parser.add_argument("--clean", action="store_true", help="清理构建目录")
    parser.add_argument("--test", action="store_true", help="测试可执行文件")
    
    args = parser.parse_args()
    
    print("🚀 开始打包OFE2E可执行文件...")
    
    if args.clean:
        clean_build_dirs()
    
    if args.tool in ["pyinstaller", "both"]:
        build_with_pyinstaller()
    
    if args.tool in ["cx_freeze", "both"]:
        build_with_cx_freeze()
    
    if args.test:
        test_executable()
    
    print("\n🎉 打包完成!")


if __name__ == "__main__":
    main()
