"""
Excel文件处理模块
负责Excel文件的读取、解析和写入操作
"""

import pandas as pd
import openpyxl
from openpyxl import load_workbook
import re
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path


class ExcelHandler:
    """Excel文件处理器"""

    def __init__(self):
        """初始化Excel处理器"""
        self.supported_formats = ['.xlsx', '.xls']

    def validate_file(self, file_path: str) -> Tuple[bool, str]:
        """
        验证Excel文件

        Args:
            file_path: 文件路径

        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        try:
            path = Path(file_path)

            # 检查文件是否存在
            if not path.exists():
                return False, f"文件不存在: {file_path}"

            # 检查文件格式
            if path.suffix.lower() not in self.supported_formats:
                return False, f"不支持的文件格式: {path.suffix}"

            # 尝试打开文件
            try:
                workbook = load_workbook(file_path, read_only=True)
                workbook.close()
                return True, ""
            except Exception as e:
                return False, f"无法打开Excel文件: {str(e)}"

        except Exception as e:
            return False, f"文件验证失败: {str(e)}"

    def get_worksheet_names(self, file_path: str) -> List[str]:
        """
        获取Excel文件中的工作表名称列表

        Args:
            file_path: Excel文件路径

        Returns:
            List[str]: 工作表名称列表
        """
        try:
            workbook = load_workbook(file_path, read_only=True)
            sheet_names = workbook.sheetnames
            workbook.close()
            return sheet_names
        except Exception as e:
            print(f"获取工作表名称失败: {e}")
            return []

    def get_column_names(self, file_path: str, sheet_name: str) -> List[str]:
        """
        获取指定工作表的列名

        Args:
            file_path: Excel文件路径
            sheet_name: 工作表名称

        Returns:
            List[str]: 列名列表
        """
        try:
            df = pd.read_excel(file_path, sheet_name=sheet_name, nrows=0)
            return df.columns.tolist()
        except Exception as e:
            print(f"获取列名失败: {e}")
            return []

    def validate_data_source_structure(self, file_path: str) -> Tuple[bool, str]:
        """
        验证数据源文件结构（必须包含"过滤条件"和"表格"工作表）

        Args:
            file_path: 数据源文件路径

        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        try:
            sheet_names = self.get_worksheet_names(file_path)

            required_sheets = ["过滤条件", "表格"]
            missing_sheets = []

            for sheet in required_sheets:
                if sheet not in sheet_names:
                    missing_sheets.append(sheet)

            if missing_sheets:
                return False, f"缺少必要的工作表: {', '.join(missing_sheets)}"

            return True, ""

        except Exception as e:
            return False, f"验证文件结构失败: {str(e)}"

    def extract_date_from_filter_sheet(self, file_path: str) -> Tuple[bool, str, Optional[str]]:
        """
        从"过滤条件"工作表的B2单元格提取日期信息

        Args:
            file_path: 数据源文件路径

        Returns:
            Tuple[bool, str, Optional[str]]: (是否成功, 错误信息, 提取的日期)
        """
        try:
            # 读取"过滤条件"工作表
            df = pd.read_excel(file_path, sheet_name="过滤条件", header=None)

            # 获取B2单元格的值（行索引1，列索引1）
            if df.shape[0] > 1 and df.shape[1] > 1:
                b2_value = df.iloc[1, 1]

                if pd.isna(b2_value):
                    return False, "B2单元格为空", None

                # 转换为字符串进行处理
                b2_str = str(b2_value)

                # 使用正则表达式提取日期
                # 匹配格式：(UTC+08:00)2025-05-19 08:00:00 ~ 2025-05-19 20:00:00
                date_pattern = r'\d{4}-\d{2}-\d{2}'
                matches = re.findall(date_pattern, b2_str)

                if matches:
                    # 取第一个匹配的日期
                    extracted_date = matches[0]
                    return True, "", extracted_date
                else:
                    return False, f"无法从B2单元格提取日期，内容: {b2_str}", None
            else:
                return False, "工作表数据不足，无法访问B2单元格", None

        except Exception as e:
            return False, f"提取日期失败: {str(e)}", None

    def read_data_from_table_sheet(self, file_path: str, data_columns: List[str],
                                 key_column: str) -> Tuple[bool, str, Optional[pd.DataFrame]]:
        """
        从"表格"工作表读取指定的数据列和主键列

        Args:
            file_path: 数据源文件路径
            data_columns: 要读取的数据列名列表
            key_column: 主键列名

        Returns:
            Tuple[bool, str, Optional[pd.DataFrame]]: (是否成功, 错误信息, 数据DataFrame)
        """
        try:
            # 读取"表格"工作表
            df = pd.read_excel(file_path, sheet_name="表格")

            # 检查列是否存在
            all_columns = data_columns + [key_column]
            missing_columns = []

            for col in all_columns:
                if col not in df.columns:
                    missing_columns.append(col)

            if missing_columns:
                return False, f"缺少列: {', '.join(missing_columns)}", None

            # 去重列名，避免重复列导致pandas Series问题
            unique_columns = list(dict.fromkeys(all_columns))  # 保持顺序的去重

            # 选择指定的列
            selected_df = df[unique_columns].copy()

            return True, "", selected_df

        except Exception as e:
            return False, f"读取表格数据失败: {str(e)}", None

    def read_mapping_data(self, file_path: str, sheet_name: str,
                         key_column: str, value_column: str) -> Tuple[bool, str, Optional[Dict[Any, Any]]]:
        """
        从映射文件读取键值映射数据

        Args:
            file_path: 映射文件路径
            sheet_name: 工作表名称
            key_column: 主键列名
            value_column: 值列名

        Returns:
            Tuple[bool, str, Optional[Dict]]: (是否成功, 错误信息, 映射字典)
        """
        try:
            # 读取指定工作表
            df = pd.read_excel(file_path, sheet_name=sheet_name)

            # 检查列是否存在
            if key_column not in df.columns:
                return False, f"映射文件中缺少主键列: {key_column}", None

            if value_column not in df.columns:
                return False, f"映射文件中缺少值列: {value_column}", None

            # 构建映射字典，过滤掉空值
            mapping_dict = {}
            for _, row in df.iterrows():
                key = row[key_column]
                value = row[value_column]

                # 跳过空值
                if pd.notna(key) and pd.notna(value):
                    mapping_dict[key] = value

            return True, "", mapping_dict

        except Exception as e:
            return False, f"读取映射数据失败: {str(e)}", None

    def write_output_file(self, output_data: pd.DataFrame, output_path: str) -> Tuple[bool, str]:
        """
        将处理结果写入Excel文件

        Args:
            output_data: 要写入的数据
            output_path: 输出文件路径

        Returns:
            Tuple[bool, str]: (是否成功, 错误信息)
        """
        try:
            # 确保输出目录存在
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            # 写入Excel文件
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                output_data.to_excel(writer, sheet_name='处理结果', index=False)

            return True, ""

        except Exception as e:
            return False, f"写入输出文件失败: {str(e)}"
