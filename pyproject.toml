[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ofe2e"
version = "1.0.0"
description = "OFE2E - Excel数据映射工具"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "OFE2E Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "OFE2E Team", email = "<EMAIL>"}
]
keywords = ["excel", "data-mapping", "gui", "pyqt5"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Topic :: Office/Business :: Financial :: Spreadsheet",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
requires-python = ">=3.11"
dependencies = [
    "PyQt5>=5.15.7",
    "pandas>=2.0.0",
    "openpyxl>=3.1.0",
    "xlrd>=2.0.1",
    "xlsxwriter>=3.1.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-qt>=4.2.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
]
build = [
    "pyinstaller>=5.13.0",
    "cx-freeze>=6.15.0",
]

[project.urls]
Homepage = "https://github.com/ofe2e/ofe2e"
Documentation = "https://github.com/ofe2e/ofe2e/docs"
Repository = "https://github.com/ofe2e/ofe2e.git"
"Bug Tracker" = "https://github.com/ofe2e/ofe2e/issues"

[project.scripts]
ofe2e = "main:main"

[project.gui-scripts]
ofe2e-gui = "main:main"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
"*" = ["*.qss", "*.json", "*.md"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"

# PyInstaller配置
[tool.pyinstaller]
name = "OFE2E"
entry-point = "main.py"
onefile = true
windowed = true
icon = "resources/icons/app.ico"
add-data = [
    "resources;resources",
    "config;config"
]
