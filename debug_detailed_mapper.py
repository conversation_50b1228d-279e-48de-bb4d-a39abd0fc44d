#!/usr/bin/env python3
"""
详细调试映射器问题
"""

import sys
import pandas as pd
from pathlib import Path
import traceback

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.excel_handler import ExcelHandler
from core.mapper import DataMapper


def debug_detailed_mapper():
    """详细调试映射器"""
    print("🔍 详细调试映射器问题")
    print("=" * 50)
    
    # 初始化
    excel_handler = ExcelHandler()
    mapper = DataMapper()
    
    # 文件路径
    data_file = "docs/box/input/数据源文件A.xlsx"
    mapping_file = "docs/box/input/映射文件A.xlsx"
    
    print(f"📊 读取数据文件: {data_file}")
    
    # 读取数据
    try:
        data_df = pd.read_excel(data_file, sheet_name="表格")
        print(f"   数据行数: {len(data_df)}")
        print(f"   数据列: {list(data_df.columns)}")
        
    except Exception as e:
        print(f"❌ 读取数据文件失败: {e}")
        return
    
    print(f"\n📋 读取映射文件: {mapping_file}")
    
    # 读取映射数据
    try:
        # 使用ExcelHandler读取映射数据
        success, error_msg, mapping_dict = excel_handler.read_mapping_data(
            mapping_file, "CVM - OMP", "接口标识", "云序号"
        )
        print(f"   映射数据读取: {'✅' if success else '❌'} {error_msg}")
        
        if success:
            print(f"   映射字典大小: {len(mapping_dict)}")
            print(f"   映射字典示例: {dict(list(mapping_dict.items())[:3])}")
            
            # 加载到映射器
            mapper.load_mapping(mapping_dict)
            print(f"   映射器加载完成")
        else:
            return
        
    except Exception as e:
        print(f"❌ 读取映射文件失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print(f"\n🧪 测试完整映射过程:")
    
    # 测试完整的map_data方法
    try:
        key_column = "UUID"
        data_columns = ["UUID[UUID]"]
        output_value_column = "云序号"
        date_period = "2025-05-19"
        
        print(f"   调用参数:")
        print(f"     data_df: {type(data_df)}, shape: {data_df.shape}")
        print(f"     key_column: {key_column}")
        print(f"     data_columns: {data_columns}")
        print(f"     output_value_column: {output_value_column}")
        print(f"     date_period: {date_period}")
        
        # 调用map_data方法
        success, error_msg, result_df = mapper.map_data(
            data_df, key_column, data_columns, output_value_column, date_period
        )
        
        print(f"\n   映射结果:")
        print(f"     成功: {'✅' if success else '❌'}")
        print(f"     错误信息: {error_msg}")
        if result_df is not None:
            print(f"     结果DataFrame: {type(result_df)}, shape: {result_df.shape}")
            print(f"     结果列: {list(result_df.columns)}")
            print(f"     前几行数据:")
            for i, row in result_df.head(3).iterrows():
                print(f"       {dict(row)}")
        else:
            print(f"     结果DataFrame: None")
    
    except Exception as e:
        print(f"❌ 映射过程失败: {e}")
        print(f"   错误类型: {type(e)}")
        print(f"   错误详情:")
        traceback.print_exc()


if __name__ == "__main__":
    debug_detailed_mapper()
