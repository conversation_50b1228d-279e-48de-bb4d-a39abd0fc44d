#!/usr/bin/env python3
"""
GUI界面测试脚本
用于快速测试GUI界面的显示效果
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_gui():
    """测试GUI界面"""
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from gui.main_window import MainWindow

        print("正在启动GUI测试...")

        # 启用高DPI支持（必须在创建QApplication之前设置）
        if hasattr(Qt, 'AA_EnableHighDpiScaling'):
            QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
            QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

        # 创建应用程序
        app = QApplication(sys.argv)

        # 设置应用程序属性
        app.setApplicationName("OFE2E GUI Test")
        app.setApplicationVersion("1.0.0")

        # 创建主窗口
        window = MainWindow()

        # 添加一些测试数据
        window.log_message("GUI界面测试启动")
        window.log_message("界面组件加载完成", "INFO")
        window.log_message("准备进行功能测试", "INFO")

        # 显示窗口
        window.show()

        print("GUI界面已启动，请在窗口中测试各项功能")
        print("关闭窗口或按Ctrl+C退出测试")

        # 运行应用程序
        sys.exit(app.exec_())

    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装PyQt5:")
        print("pip install PyQt5")
        return False
    except Exception as e:
        print(f"GUI测试失败: {e}")
        return False


if __name__ == "__main__":
    test_gui()
