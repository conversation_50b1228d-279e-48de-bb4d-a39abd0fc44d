# OFE2E 项目结构说明

## 项目概述
OFE2E是一个基于Python和PyQt5的Excel数据映射工具，支持多数据源处理、键值映射转换和结果输出。

## 目录结构

```
ofe2e/
├── src/                          # 源代码目录
│   ├── __init__.py              # 包初始化文件
│   ├── gui/                     # GUI界面模块
│   │   ├── __init__.py
│   │   └── main_window.py       # 主窗口实现
│   ├── core/                    # 核心业务逻辑（待实现）
│   │   ├── __init__.py
│   │   ├── data_processor.py    # 数据处理器
│   │   ├── excel_handler.py     # Excel文件处理
│   │   └── mapper.py            # 数据映射器
│   ├── config/                  # 配置管理
│   │   ├── __init__.py
│   │   ├── config_manager.py    # 配置管理器
│   │   └── settings.py          # 设置定义
│   └── utils/                   # 工具函数（待实现）
│       ├── __init__.py
│       ├── file_utils.py        # 文件工具
│       └── validators.py        # 数据验证
├── tests/                       # 测试代码（待实现）
│   └── __init__.py
├── resources/                   # 资源文件
│   └── sample_data/             # 示例数据
│       └── README.md
├── config/                      # 配置文件存储目录
├── docs/                        # 文档目录
│   └── PROJECT_STRUCTURE.md    # 本文件
├── requirements.txt             # 依赖包列表
├── main.py                      # 程序主入口
├── test_gui.py                  # GUI测试脚本
├── README.md                    # 项目说明
└── LICENSE                      # 许可证文件
```

## 模块说明

### GUI模块 (src/gui/)
- **main_window.py**: 主窗口实现，包含完整的用户界面
  - 数据源文件选择和管理
  - 映射源配置
  - 列配置和输出设置
  - 数据预览和处理日志
  - 配置保存和加载功能

### 配置模块 (src/config/)
- **settings.py**: 应用程序常量和默认配置
- **config_manager.py**: 配置管理器，负责配置的加载、保存和管理

### 核心模块 (src/core/) - 待实现
- **data_processor.py**: 数据处理核心逻辑
- **excel_handler.py**: Excel文件读写操作
- **mapper.py**: 数据映射转换逻辑

### 工具模块 (src/utils/) - 待实现
- **file_utils.py**: 文件操作工具函数
- **validators.py**: 数据验证工具

## 当前实现状态

### ✅ 已完成
1. **项目结构搭建**: 完整的目录结构和基础文件
2. **GUI界面设计**: 美观的主窗口界面，包含所有必要组件
3. **配置管理**: 完整的配置加载、保存和管理功能
4. **基础交互**: 文件选择、界面操作、日志显示等基础功能

### 🔄 进行中
- GUI界面的完善和优化

### ⏳ 待实现
1. **核心业务逻辑**: Excel文件处理和数据映射
2. **数据处理引擎**: pandas集成和数据转换
3. **测试模块**: 单元测试和集成测试
4. **错误处理**: 完善的异常处理和用户提示
5. **性能优化**: 大文件处理和进度显示

## 技术栈

- **GUI框架**: PyQt5
- **数据处理**: pandas + openpyxl (待集成)
- **配置管理**: JSON
- **样式**: QSS (Qt Style Sheets)

## 开发指南

### 环境设置
```bash
# 安装依赖
pip install -r requirements.txt

# 运行GUI测试
python test_gui.py

# 运行主程序
python main.py
```

### 代码规范
- 使用Python 3.7+
- 遵循PEP 8代码风格
- 添加适当的文档字符串
- 使用类型提示（推荐）

### 下一步开发计划
1. 实现Excel文件读取和解析功能
2. 开发数据映射和转换逻辑
3. 集成业务逻辑到GUI界面
4. 添加进度显示和错误处理
5. 编写测试用例
6. 性能优化和用户体验改进
