"""
数据映射器模块
负责数据映射转换逻辑
"""

import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Callable
from utils.validators import ColumnConfigValidator


class DataMapper:
    """数据映射器"""
    
    def __init__(self):
        """初始化数据映射器"""
        self.mapping_dict = {}
        self.mapping_stats = {
            'total_records': 0,
            'mapped_records': 0,
            'unmapped_records': 0,
            'unmapped_keys': []
        }
    
    def load_mapping(self, mapping_dict: Dict[Any, Any]) -> None:
        """
        加载映射字典
        
        Args:
            mapping_dict: 键值映射字典
        """
        self.mapping_dict = mapping_dict.copy()
        print(f"加载映射字典，包含 {len(self.mapping_dict)} 个映射关系")
    
    def map_data(self, data_df: pd.DataFrame, key_column: str, 
                 data_columns: List[str], value_column_config: str,
                 date_period: str, progress_callback: Optional[Callable] = None) -> Tuple[bool, str, Optional[pd.DataFrame]]:
        """
        执行数据映射转换
        
        Args:
            data_df: 源数据DataFrame
            key_column: 主键列名
            data_columns: 数据列名列表
            value_column_config: 值列配置（格式：原列名[新列名]）
            date_period: 日期周期
            progress_callback: 进度回调函数
            
        Returns:
            Tuple[bool, str, Optional[pd.DataFrame]]: (是否成功, 错误信息, 映射后的数据)
        """
        try:
            # 解析值列配置
            is_valid, error_msg, original_value_name, new_value_name = ColumnConfigValidator.validate_column_format(value_column_config)
            if not is_valid:
                return False, f"值列配置格式错误: {error_msg}", None
            
            # 使用新列名或原列名
            output_value_column = new_value_name or original_value_name
            
            # 初始化统计信息
            self.mapping_stats = {
                'total_records': len(data_df),
                'mapped_records': 0,
                'unmapped_records': 0,
                'unmapped_keys': []
            }
            
            # 创建结果列表
            result_rows = []
            
            # 处理每一行数据
            for index, row in data_df.iterrows():
                if progress_callback:
                    progress = int((index + 1) / len(data_df) * 100)
                    progress_callback(progress)
                
                # 获取主键值
                key_value = row[key_column]
                
                # 在映射字典中查找对应的值
                if pd.notna(key_value) and key_value in self.mapping_dict:
                    mapped_value = self.mapping_dict[key_value]
                    self.mapping_stats['mapped_records'] += 1
                    
                    # 构建输出行
                    output_row = {
                        '周期': date_period,
                        output_value_column: mapped_value
                    }
                    
                    # 添加数据列
                    for data_col in data_columns:
                        # 解析数据列配置
                        is_valid, _, original_name, new_name = ColumnConfigValidator.validate_column_format(data_col)
                        if is_valid:
                            output_col_name = new_name or original_name
                            output_row[output_col_name] = row[original_name]
                        else:
                            # 如果格式不正确，直接使用原列名
                            output_row[data_col] = row[data_col]
                    
                    result_rows.append(output_row)
                    
                else:
                    # 映射失败
                    self.mapping_stats['unmapped_records'] += 1
                    if pd.notna(key_value) and key_value not in self.mapping_stats['unmapped_keys']:
                        self.mapping_stats['unmapped_keys'].append(key_value)
                    
                    # 可选：是否包含未映射的记录
                    # 这里选择跳过未映射的记录，只输出成功映射的记录
                    continue
            
            # 创建结果DataFrame
            if result_rows:
                result_df = pd.DataFrame(result_rows)
                return True, "", result_df
            else:
                return False, "没有成功映射的数据记录", None
                
        except Exception as e:
            return False, f"数据映射失败: {str(e)}", None
    
    def get_mapping_statistics(self) -> Dict[str, Any]:
        """
        获取映射统计信息
        
        Returns:
            Dict[str, Any]: 映射统计信息
        """
        return self.mapping_stats.copy()
    
    def validate_mapping_coverage(self, data_df: pd.DataFrame, key_column: str) -> Tuple[float, List[Any]]:
        """
        验证映射覆盖率
        
        Args:
            data_df: 源数据DataFrame
            key_column: 主键列名
            
        Returns:
            Tuple[float, List[Any]]: (覆盖率百分比, 未覆盖的键列表)
        """
        try:
            # 获取所有唯一的键值
            unique_keys = data_df[key_column].dropna().unique()
            
            # 检查映射覆盖情况
            covered_keys = []
            uncovered_keys = []
            
            for key in unique_keys:
                if key in self.mapping_dict:
                    covered_keys.append(key)
                else:
                    uncovered_keys.append(key)
            
            # 计算覆盖率
            if len(unique_keys) > 0:
                coverage_rate = len(covered_keys) / len(unique_keys) * 100
            else:
                coverage_rate = 0.0
            
            return coverage_rate, uncovered_keys
            
        except Exception as e:
            print(f"验证映射覆盖率失败: {e}")
            return 0.0, []
    
    def preview_mapping_result(self, data_df: pd.DataFrame, key_column: str, 
                              sample_size: int = 5) -> List[Dict[str, Any]]:
        """
        预览映射结果（用于调试和验证）
        
        Args:
            data_df: 源数据DataFrame
            key_column: 主键列名
            sample_size: 预览样本数量
            
        Returns:
            List[Dict[str, Any]]: 预览结果列表
        """
        try:
            preview_results = []
            
            # 取前几行数据进行预览
            sample_df = data_df.head(sample_size)
            
            for _, row in sample_df.iterrows():
                key_value = row[key_column]
                
                preview_item = {
                    'key_value': key_value,
                    'mapped_value': self.mapping_dict.get(key_value, '未找到映射'),
                    'mapping_found': key_value in self.mapping_dict,
                    'original_data': row.to_dict()
                }
                
                preview_results.append(preview_item)
            
            return preview_results
            
        except Exception as e:
            print(f"预览映射结果失败: {e}")
            return []
    
    def export_unmapped_keys(self, output_path: str) -> Tuple[bool, str]:
        """
        导出未映射的键值到文件（用于调试）
        
        Args:
            output_path: 输出文件路径
            
        Returns:
            Tuple[bool, str]: (是否成功, 错误信息)
        """
        try:
            if not self.mapping_stats['unmapped_keys']:
                return False, "没有未映射的键值"
            
            # 创建DataFrame
            unmapped_df = pd.DataFrame({
                '未映射的键值': self.mapping_stats['unmapped_keys']
            })
            
            # 写入Excel文件
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                unmapped_df.to_excel(writer, sheet_name='未映射键值', index=False)
            
            return True, ""
            
        except Exception as e:
            return False, f"导出未映射键值失败: {str(e)}"
